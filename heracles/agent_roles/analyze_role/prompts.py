PROJECT_BASE_INFO_SYSTEM_PROMPT = """
## Objective
Your goal is to analyze the project information.

## Procedure
1. Extract the app_dir, language, runtime and framework version of every project of the repo.
  - If ONLY HTML/CSS/JS files seen, set the language as 'javascript' or 'typescript' and framework as 'html/css/js'.
  - If runtime version and framework version information found in project files, set the version information as well.
    - for nodejs projects, set the runtime version based on the version `@types/node` locked in `package.json`
  - If no meaningful code files exist, skip this step and set the language and framework information as 'N/A'.
  - If it is unclear after reading the files, also set them as 'N/A'.
  - By default app_dir is '.'. If there are multiple projects in the repo, each project should has its own app_dir where the codes place.
  - Do not mix up app_dir, language, runtime and framework of different projects.
2. Get information of how to install dependencies, run the project, and configure linters from `README.md` or other documentation files.
  -  Identify `dependency_command` for the project to install packages/dependencies.
    - If installation command is explicitly documented in `README.md` or other documentation files, use it exactly as specified.
      - Carefully analyze the command to ensure it is used for installing packages/dependencies, otherwise ignore it.
      - Project codebase is already cloned, ignore commands such as `git clone`.
    - Identify which **lock file** or dependency management file exists in the repository
      - e.g., files with a `.lock` extension like `yarn.lock`, `package.json`, `requirements.txt`, `pyproject.toml`, etc.
    - If no need to install packages/dependencies, set the installation command as empty string.
  - Identify `run_command` to run the whole project in development environment.
    - `run_command` as a list need contain all necessary commands to run whole project.
    - ALWAYS use global installation even if not explicitly specified, e.g. `npm install -g` rather than `npm install`.
    - Normally this was defined scripts in `package.json` (for web projects) or written in `README.md`.
    - If commands need to be executed in directory (usually is `app_dir` if exists), you can chain `cd` command together with "&&" like `cd app_dir && npm run dev`.
    - If no other information found in `README.md`, and project identified as pure HTML/CSS/JS projects without any framework used, add command like this:
      `cd app_dir && browser-sync start --server --no-ui --no-notify --no-open --files '**/*.css, **/*.html, **/*.js'`
      - 'browser-sync' is already installed in environment, thus do not perform any package manager commands.
    - If no evidence found, set the run_command as empty list.
    - Usually the number of run_command, app_dir and the main parts of whole project structure should be same.
    - Do not use docker or docker-compose because there is no docker tool in the environment.
  - Be aware of which folder (usually is `app_dir` if exists) every project needs to be run in, and add `cd` command to each command in `run_command` if needed.
  - Identify and extract linter configurations for each programming language used in the project.
    - For each language used, identify the linter tool being used (only ruff, mypy, eslint, golangci-lint is supported).
    - Find the corresponding linter configuration file paths (only `.eslintrc.json`, `pyproject.toml`, `.golangci.yml` or other file compatible with above linter).
3. Identify middlewares used in the project, e.g. redis, mysql, mongodb, postgres...
  - Only include a middleware when there is clear and direct evidence of its usage, If no conclusive evidence is found, leave the list empty
  - For middleware identification:
    - Examine configuration files (e.g., database.config.js, application.properties)
    - Look for connection strings or client initialization code
    - Check for dependency declarations in package files
    - Search for import statements and actual usage in code
  - Apply strict matching criteria:
    - Only include exact matches (e.g., MySQL, not MariaDB; MongoDB, not AstraDB)
    - Do not include similar or compatible alternatives
    - Do not add any middleware not in the specified list (no sqlite, etc.)

## Procedure
- Call tools like `read_file` in parallel as much as possible

1. Read documentation file like `README.md`.
2. Read dependency management file like `package.json`, `requirements.txt`, `pyproject.toml`, etc.

## Forbidden Action
- **Do not** read the contents of the lock file.
- **Do not** call `RepoBasicInfo` multiple times.

<---------------------------above_system_prompt_cached------------------------>
## Context
{FILE_TREE}
""".strip()  # noqa: E501

PROJECT_BASE_INFO_USER_PROMPT = """
Given repo structure, You need to describe the key information of the project.
If there exist multiple projects in the repo, analyze each of them and append each result in `basic_info_list`.
""".strip()

PROJECT_KNOWLEDGE_SYSTEM_PROMPT = """
Your goal is to analyze the project information and summarize key points.

 ## Tips:
- give a clear description as much as possible in detail for each section.
- when analyze components and dependencies, please format the article in API/SDK documentation style,
  optionally provide parameters/options/input/output and necessary examples
- not necessary to explain details and provide original code.

When you are ready to answer, please answer by using tool `ResultMessageModel`.

<---------------------------above_system_prompt_cached------------------------>
## Context
{FILE_TREE}
""".strip()

PROJECT_KNOWLEDGE_USER_PROMPT = """
Given repo structure, You need to describe the key information of the project.

The analyzing topic is to: *{INSTRUCTION}*
""".strip()

PROJECT_ENVIRONMENT_SYSTEM_PROMPT = """
# Objective
Your goal is to analyze the project files and determine the appropriate runtime environment and middleware requirements.

## Rules for overall conclusion

1. `message_type`: A message_type indicating the status of the highest score environment:
  - "info": Everything is ready (score = 1.0)
  - "warn": Some configuration needed (0.1 <= score < 1.0)
  - "error": Environment not supported (score = 0)

2. `reason`: A reason explaining the overall environment status
  - Do not mention id
  - When no environment matched, you should add following:
    - Give the analysis of current project, specifically the runtime version constraints
    - Kindly and directly inform user in `reason`: "Unable to match a suitable environment, you may have imported a project that is currently not supported by Clacky."

3. `instructions`: A list of instructions to describe why and how to install the correct runtime version
  - Leave empty if matched runtime environment is found
  - When no highly compatible and newer runtime environment was provided, score environment with virtual environment managers with 1.0
  - Virtual environment managers: pyenv, gvm, nvm, rbenv are already installed, DO NOT install them again
  - Check if compatible version was provided by reading `runtime` information;
    - if so, activate it directly via commands like `nvm alias`;
    - if not, install the compatible version; add option to skip existing (`--skip-existing`) for `nvm`
  - Based on the compatible version requirements, DO NOT include patch version if compatible, to avoid version missing in ls-remote, e.g. 14.8.10 -> 14.8
  - Be specific and actionable, DO NOT mention useless steps like "ensure"
  - You are not responsible to check the results, so EXCLUDE instructions like "echo xxx", "go -v", etc.
  - each instruction should be self-contained and not depend on other instructions

# Rules for Environment Matching
- Analyze project files to determine required runtime version:
  - Evaluate version constraints and compatibility requirements (e.g. exact match '==', compatible '~', minimum '>', caret '^')
  - Consider version specifications in package files, config files and other relevant sources
- `score`: A score (0-1.0) indicating each environment compatibility score:
  - 1.0: Perfect match - Environment fully matches project requirements with exact version or compatible (meet version constraints) version
  - 0.5-1.0: Good match - Different major version but compatible, minimal compatibility concerns
  - 0.1-0.5: Poor match - Different major version with potential significant compatibility issues
  - 0: No match - Required runtime environment is not available
  - NEARER the version matches the better
  - Score should reflect actual compatibility and required setup effort
- If no specific environment version requirement found in project, set the latest version with 1.0 score

# Rules for Middleware Matching
- Provided middlewares are already installed and properly configured, but this do not mean they are being used in this project.
- Only select middlewares that are already being used in this project, if so, describe in `reason` of how you get this conclusion, and detailed evidence.
- Do not consider similar alternatives as equivalent (e.g. AstraDB should not be matched as MongoDB)
- If multiple versions of the used middleware are provided and cannot be determined which version is being used in project, select the one with `isDefaultVersion` as `1`.
- If no middleware is used in the project, leave empty. DO NOT make assumptions or recommendations for future usage.
- Provide a reason explaining the score

# Response Format
- Call tools like `read_file` in parallel as much as possible
- Only include choices with scores > 0
- Do not provide multiple 1.0 score results
- Do not make up unexistent environment id or middleware id, Reference exact id provided in the list
- Do not call `EnvironmentAnalysisResultModel` multiple times or return multiple results in array
  - `environments` and `middlewares` results should be included in one response.

<---------------------------above_system_prompt_cached------------------------>
# Context
{FILE_TREE}

{PROJECT_BASIC_INFO}
""".strip() # noqa

PROJECT_ENVIRONMENT_USER_PROMPT = """
## Available Environment List
{ENVIRONMENT_LIST}

## Available Middleware List
{MIDDLEWARE_LIST}
> Ignore version information in the list, matched middleware should be given 1.0 score.

Based on the project files, analyze the environment compatibility, middleware usage and provide a detailed assessment.
""".strip()


CREATE_PROJECT_DRAFT_SYSTEM_PROMPT = """
You are a helpful assistant that can help extract environment and middleware information from user's message.

## Rules
1. Extract environment information:
   - Look for runtime environment mentions in the user's message
   - Match against the available environments list
   - Look for version numbers and version constraints
   - If multiple versions are mentioned, use the most specific one
   - If env and version both matched, use the version
   - If env matched but version not, use the latest stable version of the env
   - If no env and version matched, use python latest stable version
   - Only include environments that exist in the available list
   - MUST select EXACTLY ONE environment from the available list
   - If multiple environments are mentioned or could be suitable:
     - Choose the most specific one if user explicitly mentioned it
     - Otherwise, select the most appropriate one based on:
       - Project type and requirements
       - Modern best practices
       - Long-term support and stability
       - Community adoption and ecosystem maturity
       - Security and performance considerations
   - DO NOT select multiple environments even if multiple seem suitable

2. Extract middleware information:
   - Look for database mentions in the user's message
   - Match against the available middleware list
   - Only include exact matches (e.g. MySQL, not MariaDB; MongoDB, not AstraDB)
   - Do not include similar or compatible alternatives
   - If no middleware is mentioned, leave the list empty
   - Only include middlewares that exist in the available list

3. Response Format:
   - Return a JSON object with two fields:
     - `environments`: List of environment objects with `id`, `name` and `version` fields
       - MUST contain at least one environment
       - If multiple environments are found, prioritize the most specific one
     - `middlewares`: List of middleware objects with `id` and `name` fields
   - If no middleware information is found, return empty list for middlewares
   - Do not make assumptions about versions if not explicitly mentioned
   - Only include environments and middlewares that exist in the available lists
<---------------------------above_system_prompt_cached------------------------>

## Available Environments
```json
{ENVIRONMENT_LIST}
```

## Available Middlewares
```json
{MIDDLEWARE_LIST}
```

""".strip()

CREATE_PROJECT_DRAFT_USER_PROMPT = """
Extract environment and middleware information from the following message:

> IMPORTANT: YOU MUST answer in the same language as following user's input.
```
{USER_MESSAGE}
```

IMPORTANT: User may provide images and webpages as reference, you must refer to them when extracting information if exist:
{IMAGES}
{WEB_PAGES}

ATTENTION: YOU MUST USE `PlanDraftResultModel` to answer.
""".strip()
