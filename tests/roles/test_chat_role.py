import pytest
import litellm
from heracles.agent_roles.chat_role import <PERSON><PERSON><PERSON><PERSON>
from heracles.agent_roles.role_action import Agent<PERSON><PERSON><PERSON><PERSON><PERSON>pecAction, AgentRoleNullAction
from heracles.core.schema import FileSnippet, ProjectErrorMessage
from heracles.agent_controller.llm import LLM
from heracles.core.schema.models import TaskIntentModel
from heracles.core.schema.models.task_model import TaskStepModel, TaskActionModel, ActionType
from heracles.core.exceptions import IDEServerFileNotFoundError, IDEServerFileBinaryError, IDEServerFunCallException, AgentRunException
from heracles.agent_roles.utils import extract_user_message
from heracles.core.utils import DotDict

@pytest.mark.asyncio
async def test_chat_role_chunk_message(create_workspace, mocker):
    workspace = await create_workspace
    chat_role = ChatRole(workspace)

    # Mock LLM response for chunk message
    async def mock_async_send_chunk(*args, **kwargs):
        chunk_callback = kwargs.get('chunk_callback')
        if chunk_callback:
            await chunk_callback("Chunk message part 1")
            await chunk_callback("Chunk message part 2")
        return "Final response"

    mocker.patch.object(LLM, 'async_send', side_effect=mock_async_send_chunk)
    mock_trigger = mocker.patch.object(workspace, 'trigger')

    # Test chunk message callback
    await chat_role.run("Test message", need_consider_task=False)
    mock_trigger.assert_any_call('chunk_message', "Chunk message part 1")
    mock_trigger.assert_any_call('chunk_message', "Chunk message part 2")

@pytest.mark.asyncio
async def test_chat_role_tool_callback(create_workspace, mocker):
    workspace = await create_workspace
    chat_role = ChatRole(workspace)

    # Mock LLM response for tool callback
    async def mock_async_send_tool(*args, **kwargs):
        completion_objs = [
            {"role": "assistant", "content": "let's read file content"},
            {"role": "assistant", "content": "", "tool_calls": [{
                "id": "1234abcd",
                "function": {
                    "name": "read_file_content",
                    "arguments": '{"path": "test/path1"}'
                }
            }]},
            {"role": "assistant", "content": "success"}
        ]
        if not hasattr(test_chat_role_tool_callback, 'triggered'):
            for completion_obj in completion_objs:
                yield litellm.ModelResponse(choices=[
                    {
                        "delta": completion_obj,
                        "finish_reason": None
                    }
                ], stream=True)
            test_chat_role_tool_callback.triggered = True
        else:
            yield litellm.ModelResponse(choices=[
                {
                    "delta": completion_objs[-1],
                    "finish_reason": None
                }
            ], stream=True)

    mocker.patch.object(litellm, 'acompletion', side_effect=mock_async_send_tool)
    mocker.patch.object(chat_role, 'memory')

    mock_trigger = mocker.patch.object(workspace, 'trigger')

    # Test tool callback
    await chat_role.run("Test message", need_consider_task=False)
    called_args = mock_trigger.call_args_list
    message_found = False
    for args in called_args:
        print(args)
        if args[0][0] == 'chunk_message' and 'test/path1' in args[0][1] and 'read file content' in args[0][1].lower():
            message_found = True
            break
    assert message_found

@pytest.mark.asyncio
async def test_chat_role_run(create_workspace, mocker):
    workspace = await create_workspace
    chat_role = ChatRole(workspace)

    # Mock LLM response for TaskIntentModel
    mocker.patch.object(chat_role, 'aask', return_value=TaskIntentModel(goal="Test Goal", goal_detail="Test Goal Detail"))

    # Test case: need_consider_task=True
    res = await chat_role.run("Test message", need_consider_task=True)
    assert isinstance(res, AgentRoleCreateSpecAction)
    assert res.goal == "Test Goal"
    assert res.goal_detail == "Test Goal Detail"

    # Mock LLM response for TaskStepModel
    mocker.patch.object(chat_role, 'aask', return_value=TaskStepModel(
        title="Test Step",
        task_actions=[
            TaskActionModel(action=ActionType.RUN_COMMAND, command="Test Command"),
        ]
    ))

    # Test case: LLM returns a string with error reference
    mocker.patch.object(chat_role, 'aask', return_value="Test String Response")
    mocker.patch.object(
        workspace.playground.ide_server_client,
        'agent_read_file',
        return_value="file content"
    )
    workspace.smart_detect.errors = [
        ProjectErrorMessage(title='error-title', ref_id='error-id', category='terminal', content='Error Content')
    ]
    res = await chat_role.run("Test message@@user_context[file://test/path1:1-2, error://error-id]@@", need_consider_task=False)
    assert isinstance(res, AgentRoleNullAction)
    assert res.result == "Test String Response"

@pytest.mark.asyncio
async def test_extract_referenced_file_contents(create_workspace, mocker):
    workspace = await create_workspace
    chat_role = ChatRole(workspace)

    # Test case: no reference
    message_content, content_dict = await extract_user_message("content", chat_role.workspace)
    assert content_dict['files'] == []

    # Test case: file reference found
    mocker.patch.object(workspace.playground.ide_server_client, 'agent_read_file', return_value="file content")
    message_content, content_dict = await extract_user_message(
        "content@@user_context[file://test/path1:1-2]@@", chat_role.workspace)
    assert len(content_dict['files']) == 1
    assert content_dict['files'][0] == FileSnippet(path="test/path1", content="   1: file content", row_start=1, row_end=2) # noqa

    # Test case: file reference with dynamic id(brackets) found
    mocker.patch.object(workspace.playground.ide_server_client, 'agent_read_file', return_value="file content")
    message_content, content_dict = await extract_user_message(
        "content@@user_context[file://app/pages/[id]/page.tsx]@@", chat_role.workspace)
    assert len(content_dict['files']) == 1
    assert content_dict['files'][0].path == "app/pages/[id]/page.tsx"

    # Test case: file not found
    mocker.patch.object(workspace.playground.ide_server_client, 'agent_read_file', side_effect=IDEServerFileNotFoundError('File not found'))
    mocker.patch.object(chat_role.logger, 'warning')
    message_content, content_dict = await extract_user_message(
        "content@@user_context[file://nonexistent/path]@@", chat_role.workspace)
    assert len(content_dict['files']) == 0
    chat_role.logger.warning.assert_called_with("Referenced file `nonexistent/path` not found")

    # Test case: binary file error
    mocker.patch.object(workspace.playground.ide_server_client, 'agent_read_file', side_effect=IDEServerFileBinaryError("File is binary or too big"))  # noqa: E501
    mocker.patch.object(chat_role.logger, 'warning')
    message_content, content_dict = await extract_user_message(
        "content@@user_context[file://binary/path]@@", chat_role.workspace)
    assert len(content_dict['files']) == 0
    chat_role.logger.warning.assert_called_with("Referenced file `binary/path` is binary or too big")

    # Test case: function call exception
    mocker.patch.object(workspace.playground.ide_server_client, 'agent_read_file', side_effect=IDEServerFunCallException("Function call error"))  # noqa: E501
    mocker.patch.object(chat_role.logger, 'warning')
    message_content, content_dict = await extract_user_message(
        "content@@user_context[file://error/path]@@", chat_role.workspace)
    assert message_content == "content"
    assert len(content_dict['files']) == 0
    chat_role.logger.warning.assert_called_with("Referenced file `error/path` error: Function call error")

@pytest.mark.asyncio
async def test_extract_referenced_playbooks(create_workspace, mocker):
    workspace = await create_workspace
    chat_role = ChatRole(workspace)
    # Test case: playbooks reference found
    message_content, content_dict = await extract_user_message(
        "content@@user_context[playbook://Handling .gitignore File]@@", chat_role.workspace)
    assert len(content_dict['playbooks']) == 1
    assert content_dict['playbooks'][0].title == "Handling .gitignore File"

    # Test case: playbooks reference not found
    mocker.patch.object(chat_role.logger, 'warning')
    message_content, content_dict = await extract_user_message(
        "content@@user_context[playbook://nonexistent-id]@@", chat_role.workspace)
    assert message_content == "content"
    assert len(content_dict['files']) == 0
    assert len(content_dict['playbooks']) == 0
    chat_role.logger.warning.assert_called_with("Referenced playbook `nonexistent-id` not found")

@pytest.mark.asyncio
async def test_extract_referenced_errors(create_workspace, mocker):
    workspace = await create_workspace
    chat_role = ChatRole(workspace)

    # Test case: errors reference found
    workspace.smart_detect.errors = [
        ProjectErrorMessage(title='error-title', ref_id='error-id', category='terminal', content='Error Content')
    ]
    message_content, content_dict = await extract_user_message(
        "content@@user_context[error://error-id]@@", chat_role.workspace)
    assert len(content_dict['errors']) == 1
    assert content_dict['errors'][0].ref_id == "error-id"
    assert content_dict['errors'][0].content == "Error Content"

    # Test case: errors reference not found
    mocker.patch.object(chat_role.logger, 'warning')
    message_content, content_dict = await extract_user_message(
        "content@@user_context[error://nonexistent-id]@@", chat_role.workspace)
    assert message_content == "content"
    assert len(content_dict['errors']) == 0
    chat_role.logger.warning.assert_called_with("Referenced error message `nonexistent-id` not found")

@pytest.mark.asyncio
async def test_extract_referenced_webpages(create_workspace, mocker):
    workspace = await create_workspace
    chat_role = ChatRole(workspace)

    # Test case: webpage reference found
    mocker.patch.object(chat_role.workspace.tools, 'read_webpage', return_value="webpage content")
    message_content, content_dict = await extract_user_message(
        "content@@user_context[webpage://https://www.baidu.com]@@", chat_role.workspace)
    assert len(content_dict['webpages']) == 1
    assert content_dict['webpages'][0].url == "https://www.baidu.com"
    assert content_dict['webpages'][0].content == "webpage content"

    # Test case: webpage reference not found
    mock_logger = mocker.patch.object(chat_role.logger, 'warning')
    mocker.patch.object(
        chat_role.workspace.tools,
        'read_webpage',
        side_effect=AgentRunException("Failed to read web page: nonexistent-url")
    )
    message_content, content_dict = await extract_user_message(
        "content@@user_context[webpage://nonexistent-url]@@", chat_role.workspace)
    assert message_content == "content"
    assert len(content_dict['webpages']) == 0
    mock_logger.assert_called_with("Referenced web `nonexistent-url` error: Failed to read web page: nonexistent-url")

@pytest.mark.asyncio
async def test_extract_referenced_images(create_workspace, mocker):
    workspace = await create_workspace
    chat_role = ChatRole(workspace)

    # Test case: image reference found
    mock_response = mocker.MagicMock()
    mock_response.status = 200

    mock_session = mocker.MagicMock()
    mock_session.__aenter__.return_value = mock_session
    mock_session.__aexit__.return_value = None
    mock_session.get.return_value.__aenter__.return_value = mock_response
    mocker.patch('aiohttp.ClientSession', return_value=mock_session)

    message_content, content_dict = await extract_user_message(
        "content@@user_context[image_url://https://example.com/image.jpg]@@", chat_role.workspace)
    assert len(content_dict['images']) == 1
    assert content_dict['images'][0] == "https://example.com/image.jpg"

    # Test case: image reference not found
    mock_response.status = 404
    mock_logger = mocker.patch.object(chat_role.logger, 'error')
    message_content, content_dict = await extract_user_message(
        "content@@user_context[image_url://http://nonexistent-image-url]@@", chat_role.workspace)
    assert message_content == "content"
    assert len(content_dict['images']) == 0
    mock_logger.assert_called_with(
        "Referenced image `http://nonexistent-image-url` error: "
        "Invalid image URL: http://nonexistent-image-url, only support http or https URL, and image file extension ('.png', '.jpg', '.jpeg', '.gif', '.webp', 'amazonaws.com')"  # noqa: E501
    )

@pytest.mark.asyncio
async def test_extract_referenced_images_aws(create_workspace, mocker):
    workspace = await create_workspace
    chat_role = ChatRole(workspace)

    # Test case: image reference found
    mock_response = mocker.MagicMock()
    mock_response.status = 200

    mock_session = mocker.MagicMock()
    mock_session.__aenter__.return_value = mock_session
    mock_session.__aexit__.return_value = None
    mock_session.get.return_value.__aenter__.return_value = mock_response
    mocker.patch('aiohttp.ClientSession', return_value=mock_session)
    message_content, content_dict = await extract_user_message(
        "content@@user_context[image_url://https://backend-staging-qwertyuiopas3456.s3-accelerate.amazonaws.com/images/2025/06/08/01920a1e-5545-76d0-b3e3-09df42e2e8ef/screenshot_17493811343291749381134491]@@", chat_role.workspace) # noqa: E501
    assert len(content_dict['images']) == 1
    assert content_dict['images'][0] == "https://backend-staging-qwertyuiopas3456.s3-accelerate.amazonaws.com/images/2025/06/08/01920a1e-5545-76d0-b3e3-09df42e2e8ef/screenshot_17493811343291749381134491" # noqa: E501


@pytest.mark.asyncio
async def test_chat_role_scene_need_consider_task(create_workspace, mocker):
    workspace = await create_workspace
    chat_role = ChatRole(workspace)
    mocker.patch.object(chat_role, 'aask', return_value=TaskIntentModel(goal="Test Goal", goal_detail="Test Goal Detail"))
    res = await chat_role.run("Test message", need_consider_task=True)
    assert isinstance(res, AgentRoleCreateSpecAction)

@pytest.mark.asyncio
async def test_chat_role_scene_need_not_consider_task(create_workspace, mocker):
    workspace = await create_workspace
    chat_role = ChatRole(workspace)
    mocker.patch.object(chat_role, 'aask', return_value=TaskIntentModel(goal="Test Goal", goal_detail="Test Goal Detail"))
    res = await chat_role.run("Test message", need_consider_task=True)
    assert isinstance(res, AgentRoleCreateSpecAction)

@pytest.mark.asyncio
async def test_chat_role_tool_callback_no_error(create_workspace, mocker):
    workspace = await create_workspace
    chat_role = ChatRole(workspace)

    mock_log_error = mocker.patch.object(chat_role.logger, 'error')

    tool_call = DotDict({
        "id": "1234abcd",
        "function": {
            "name": "read_file_content",
            "arguments": '{"path": "test/path1"}'
        }
    })
    await chat_role.trigger_tool_callback(tool_call, "end")
    mock_log_error.assert_not_called()

    tool_call = DotDict({
        "id": "1234abcd",
        "function": {
            "name": "run_project",
            "arguments": '{}'
        }
    })
    await chat_role.trigger_tool_callback(tool_call, "end")
    mock_log_error.assert_not_called()
