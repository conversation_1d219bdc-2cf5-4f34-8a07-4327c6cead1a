import pytest
import os

from heracles.core.schema import FileSnippet
from heracles.core.exceptions import AgentRunException
from heracles.agent_roles.code_role.utils import generate_file_diff, match_best_code_segment, \
    apply_edited_snippets, fix_code_indention, check_syntax

def test_diff_result():
    original_file_content = "print('Hello, world!')"
    edited_file_content = "print('Hello, world!')"

    diff_result = generate_file_diff(original_file_content, edited_file_content)
    assert "" == diff_result

    original_file_content = "print('Hello, world!')\nprint('This is the original file.')"
    edited_file_content = "print('Hello, world!')\nprint('This is the edited file.')"

    diff_result = generate_file_diff(original_file_content, edited_file_content)
    assert "+ 1 lines, - 1 lines" in diff_result, "diff_result 不包含正确内容"

def test_match_best_code_segment_in_real():
    with open(os.path.join(os.path.dirname(__file__) + '/../data/aider_check_autocomplete-checks.py.dt'), 'r') as f:
        original_file_content = f.read()

    snippet = """class BaseModelAdminChecks:
    def check(self, admin_obj, **kwargs):
        return [
            ...
        ]"""

    matched_snippet = match_best_code_segment(original_file_content, snippet)
    assert matched_snippet.content == """class BaseModelAdminChecks:
    def check(self, admin_obj, **kwargs):
        return [
            *self._check_autocomplete_fields(admin_obj),
            *self._check_raw_id_fields(admin_obj),
            *self._check_fields(admin_obj),
            *self._check_fieldsets(admin_obj),
            *self._check_exclude(admin_obj),
            *self._check_form(admin_obj),
            *self._check_filter_vertical(admin_obj),
            *self._check_filter_horizontal(admin_obj),
            *self._check_radio_fields(admin_obj),
            *self._check_prepopulated_fields(admin_obj),
            *self._check_view_on_site_url(admin_obj),
            *self._check_ordering(admin_obj),
            *self._check_readonly_fields(admin_obj),
        ]"""

def test_match_best_code_segment():
    file_content = "print('Hello, world!')\nprint('This is the original file.')"
    code_segment = "print('This is the original file.')"
    result = match_best_code_segment(file_content, code_segment, 80)
    assert result.row_start == 1
    assert result.row_end == 1

def test_apply_edited_snippets():
    original_file_content = "print('Hello, world!')\n    print('This is the original file.')"
    edited_file_snippets = [
        FileSnippet(content="# This is a comment.\nprint('This is the edited file.')"),
    ]
    with pytest.raises(AgentRunException, match='Snippet `row_start` or `row_end` is not set.'):
        new_file_content = apply_edited_snippets(original_file_content, edited_file_snippets)

    edited_file_snippets = [
        FileSnippet(content="# This is a comment.\nprint('This is the edited file.')",
                    row_start=1, row_end=2)
    ]
    new_file_content = apply_edited_snippets(original_file_content, edited_file_snippets)
    assert new_file_content == "print('Hello, world!')\n    # This is a comment.\n    print('This is the edited file.')"

    original_file_content = "print('Hello, world!')\n\n    print('This is the original file.')"
    edited_file_snippets = [
        FileSnippet(row_start=1, row_end=2, content="# This is a comment.\n\nprint('This is the edited file.')"),
    ]
    new_file_content = apply_edited_snippets(original_file_content, edited_file_snippets)
    assert new_file_content == "print('Hello, world!')\n    # This is a comment.\n\n    print('This is the edited file.')"

def test_apply_edited_snippets_repeat():
    origin_file_content = """
package com.company.project.service.impl;

import com.company.project.dao.UserMapper;
import com.company.project.model.User;
import com.company.project.service.UserService;
import com.company.project.core.AbstractService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.List;
import java.util.Comparator;

/**
 * Created by CodeGenerator on 2024/11/18.
 */
@Service
@Transactional
public class UserServiceImpl extends AbstractService<User> implements UserService {
    @Resource
    private UserMapper userMapper;

    public void addUser(User user) {
        user.setRegisterDate(new java.util.Date());
        String encryptedPassword = encryptPassword(user.getPassword());
        user.setPassword(encryptedPassword);
        userMapper.insert(user);
    }

    public boolean validateCredentials(String username, String password) {
        String encryptedPassword = encryptPassword(password);
        User user = userMapper.findByUsernameAndPassword(username, encryptedPassword);
        boolean validCredentials = user != null;
        return validCredentials;
    }

    public void save(User user) {
        addUser(user);
    }

    @Override
    public String generateToken() {
        return java.util.UUID.randomUUID().toString().replace("-", "");
    }

    @Override
    public List<User> fetchSortedUsers() {
        return userMapper.fetchSortedUsers();
    }

    private String encryptPassword(String password) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(password.getBytes());
            byte[] byteData = md.digest();

            // Convert byte array to hex format
            StringBuilder sb = new StringBuilder();
            for (byte b : byteData) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("MD5 encryption failed", e);
        }
    }
}
"""

    origin_task = { #noqa
        "tool_calls": [
            {
                "index": 0,
                "function": {
                    "arguments": {
                        "tasks": [
                            {
                                "original": "import com.company.project.dao.UserMapper;\nimport com.company.project.model.User;\nimport com.company.project.service.UserService;", #noqa
                                "edited": "import com.company.project.dao.UserMapper;\nimport com.company.project.model.User;\nimport com.company.project.service.UserService;\nimport org.springframework.data.redis.core.RedisTemplate;", #noqa
                                "thought": "Add the RedisTemplate import to use Redis operations."
                            },
                            {
                                "original": "    @Resource\n    private UserMapper userMapper;",
                                "edited": "    @Resource\n    private UserMapper userMapper;\n    \n    @Resource\n    private RedisTemplate<String, Object> redisTemplate;", #noqa
                                "thought": "Inject RedisTemplate to interact with Redis."
                            },
                            {
                                "original": "    }\n",
                                "edited": "    }\n    \n    public void saveUserToRedis(User user) {\n        redisTemplate.opsForValue().set(\"user:\" + user.getId(), user);\n    }\n\n    public User getUserFromRedis(String userId) {\n        return (User) redisTemplate.opsForValue().get(\"user:\" + userId);\n    }\n", #noqa
                                "thought": "Add methods saveUserToRedis and getUserFromRedis for Redis storage and retrieval."
                            }
                        ]
                    },
                    "name": "IterableEditedFileSnippet"
                },
                "id": "call_pnixzpABzHuWWpPBNANzajYT",
                "type": "function"
            }
        ],
        "function_call": None
    }

    edited_file_snippet_dict = {
        'path': '',
        'content': '    }\n    \n    public void saveUserToRedis(User user) {\n        redisTemplate.opsForValue().set("user:" + user.getId(), user);\n    }\n\n    public User getUserFromRedis(String userId) {\n        return (User) redisTemplate.opsForValue().get("user:" + userId);\n    }', #noqa
        'row_start': 30,
        'row_end': 30
    }
    edited_file_snippets = [FileSnippet.model_validate(edited_file_snippet_dict)]

    new_file_content = apply_edited_snippets(origin_file_content, edited_file_snippets)
    assert new_file_content.count('public void saveUserToRedis(User user)') == 1

def test_fix_code_indention_add_spaces():
    selected_snippets = """    def _can_fuse_epilogue_impl(
        self,
        cuda_template_buffer: CUDATemplateBuffer,
        epilogue_nodes: List[ir.IRNode],
        additional_node: ir.IRNode,
    ) -> bool:"""
    edited_snippets = """def _can_fuse_epilogue_impl(
    self,
    cuda_template_buffer: CUDATemplateBuffer,
    epilogue_nodes: List[ir.IRNode],
    additional_node: ir.IRNode,
) -> bool:"""
    selected_lines = selected_snippets.split('\n')
    edited_lines = edited_snippets.split('\n')
    edited_lines = fix_code_indention(selected_lines, edited_lines)
    assert edited_lines == selected_lines

def test_fix_code_indention_mixed_diffs():
    """测试混合缩进差异时选择最常见的最小差异值"""
    selected_lines = [
        '    line1',  # 4空格
        '  line2',    # 2空格
        '      line3'  # 6空格
    ]
    edited_lines = [
        '  line1',    # 差异+2
        'line2',      # 差异+2
        '    line3'   # 差异+2
    ]
    # 期望统一补2空格缩进
    expected = [
        '    line1',
        '  line2',
        '      line3'
    ]
    adjusted = fix_code_indention(selected_lines, edited_lines)
    assert adjusted == expected

def test_fix_code_indention_negative_diff():
    """测试反向缩进差异（原缩进 < 新缩进）"""
    selected_lines = [
        '  print()',
        '    if x:'
    ]
    edited_lines = [
        'print()',   # 差异-2
        '  if x:'    # 差异-2
    ]
    # 期望增加2空格缩进
    expected = [
        '  print()',
        '    if x:'
    ]
    adjusted = fix_code_indention(selected_lines, edited_lines)
    assert adjusted == expected

def test_fix_code_indention_blank_lines():
    """测试包含空行的处理"""
    selected_lines = [
        '    print()',
        '',  # 原文件中的空行
        '    # comment'
    ]
    edited_lines = [
        'print()',   # 缩进差异-4
        '  ',        # 带空格的空行需要被处理
        '# comment'  # 缩进差异-4
    ]
    expected = [
        '    print()',
        '',          # 期望结果保持无缩进的空行
        '    # comment'
    ]
    adjusted = fix_code_indention(selected_lines, edited_lines)
    assert adjusted == expected

def test_fix_code_indention_with_leading_blank_line_and_new_line():
    """测试包含前置空行和新增行时，继承缩进规则"""
    selected_lines = [
        '',  # 原始空行（索引0）
        "    print('This is the original file.')"  # 索引1
    ]
    edited_lines = [
        '# This is a comment.',  # 替换索引0
        '',                       # 新增行（对应无原始行）
        "print('This is the edited file.')"  # 替换索引1
    ]
    expected = [
        '    # This is a comment.',  # 继承上下文4空格
        '',                          # 新增空行零缩进
        "    print('This is the edited file.')"  # 保持4空格
    ]
    adjusted = fix_code_indention(selected_lines, edited_lines)
    assert adjusted == expected

def test_fix_code_indention_with_leading_blank_line():
    """测试包含前置空行的缩进处理"""
    selected_lines = [
        '',          # 原文件中的前置空行
        '    print()'
    ]
    edited_lines = [
        '',          # 需要继承前置空行的上下文缩进（实际应为4空格）
        '# comment',  # 需要继承前置空行的上下文缩进（实际应为4空格）
        'print()'    # 需要继承4空格缩进
    ]
    expected = [
        '',
        '    # comment',
        '    print()'
    ]
    adjusted = fix_code_indention(selected_lines, edited_lines)
    assert adjusted == expected

def test_fix_code_indention_uneven_lines():
    """测试行数不一致时新增行继承缩进规则"""
    selected_lines = [
        '    line1',
        '    line2'
    ]
    edited_lines = [
        'line1',      # 缩进差异+4
        'line2',      # 缩进差异+4
        'new_line',   # 新增行应继承+4
        '    if x:',  # 原缩进4，应变为8
        '  pass'      # 原缩进2，应变为6
    ]
    expected = [
        '    line1',
        '    line2',
        '    new_line',  # 继承+4调整
        '        if x:',
        '      pass'
    ]
    adjusted = fix_code_indention(selected_lines, edited_lines)
    assert adjusted == expected

def test_fix_code_indention_keep_internal_consistency():
    """测试保持编辑内容内部缩进关系"""
    selected_lines = [
        'def test():',
        '    pass'
    ]
    edited_lines = [
        'def test():',
        '    if x:',
        '        print()',  # 内部缩进应保持
        '    return'
    ]
    # 即使没有缩进差异也应保持原样
    adjusted = fix_code_indention(selected_lines, edited_lines)
    assert adjusted == edited_lines

def test_fix_code_indention_partial_overlap():
    """测试部分行重叠时的缩进继承"""
    selected_lines = [
        '    start = 0',
        '    end = 10'
    ]
    edited_lines = [
        'start = 0',        # 缩进差异+4
        'end = 10',         # 缩进差异+4
        'new_var = 20',     # 应继承+4
        '    # 注释保持不变',  # 原缩进4，应变为8
        'print()'           # 应继承+4
    ]
    expected = [
        '    start = 0',
        '    end = 10',
        '    new_var = 20',
        '        # 注释保持不变',
        '    print()'
    ]
    adjusted = fix_code_indention(selected_lines, edited_lines)
    assert adjusted == expected

def test_fix_code_indention_charset_update():
    """测试数据库连接字符串字符集更新"""
    selected_lines = [
        '\tcredentials := fmt.Sprintf("%s:%s@(%s:%s)/?charset=utf8&parseTime=True", user, pass, host, port)',
        '',
        '\tdatabase, err := sql.Open("mysql", credentials)',
        '',
        '\tif err != nil {'
    ]
    edited_lines = [
        '\tcredentials := fmt.Sprintf("%s:%s@(%s:%s)/?charset=utf8mb4&parseTime=True", user, pass, host, port)',
        '',
        '\tdatabase, err := sql.Open("mysql", credentials)',
        '',
        '\tif err != nil {'
    ]
    adjusted = fix_code_indention(selected_lines, edited_lines)
    assert adjusted == edited_lines


def test_match_best_code_segment_with_triple_quote():
    with open(os.path.join(os.path.dirname(__file__) + '/../data/aider-cuda_cpp_scheduling.py.dt'), 'r') as f:
        original_file_content = f.read()
    snippet = """def _can_fuse_epilogue_impl(
    cuda_template_buffer: CUDATemplateBuffer,
    epilogue_nodes: List[ir.IRNode],
    additional_node: ir.IRNode,
) -> bool:
    \"\"\"
    Check if the given node can be fused with the epilogue. At the moment, Kernels
    support fusion with Pointwise operations, wrapped in (named) ComputedBuffer nodes.

    Args:
        cuda_template_buffer : A CUDATemplateBuffer object representing the CUDA template and it's result buffer
        epilogue_nodes : List[ir.Buffer]: The list of already fused epilogue nodes.
        additional_node: The ir.Buffer node to be checked if it can be fused with the epilogue.
    Returns:
    - bool: True if the given node can be fused with the epilogue, False otherwise.

    \"\"\"
    if not isinstance(cuda_template_buffer, CUDATemplateBuffer):
        return False
    if not cuda_template_buffer.template.can_fuse_epilogue:
        # The used GEMM op does not support fusing epilogues
        return False
    if not isinstance(additional_node, ComputedBuffer):
        return False
    if not isinstance(additional_node.data, Pointwise):
        return False
    # We can fuse a Pointwise op that depends on the last fused epilogue node
    # if any. If there is no epilogue node yet, it needs to depend on the template
    # node
    node_name = additional_node.get_computed_buffer_name()
    if node_name is None:
        return False

    if len(epilogue_nodes) == 0:
        if cuda_template_buffer.name not in additional_node.get_read_names():
            return False
    else:
        last_epilogue_node = epilogue_nodes[-1]
        assert isinstance(last_epilogue_node, ir.ComputedBuffer)
        last_epilogue_name = (
            last_epilogue_node.name
            if last_epilogue_node.name is not None
            else last_epilogue_node.data.name
        )
        if last_epilogue_name not in additional_node.get_read_names():
            return False
    if additional_node.layout != cuda_template_buffer.layout:
        return False
    try:
        from torch._inductor.codegen.cuda.cutlass_epilogue_gen import (
            CutlassEVTEpilogueArgumentFormatter,
            CutlassEVTEpilogueTypeFormatter,
        )

        CutlassEVTEpilogueTypeFormatter.ir_to_evt_string(
            cast(str, cuda_template_buffer.name), "anything", [additional_node]
        )
        CutlassEVTEpilogueArgumentFormatter.ir_to_evt_argument_string(
            cast(str, cuda_template_buffer.name), [additional_node]
        )
    except CUTLASSEVTOpNotImplementedError as e:
        not_implemented_op = str(e)
        if not_implemented_op.startswith("_op_"):
            not_implemented_op = not_implemented_op[4:]
            log.warning(
                f"Cannot fuse epilogue node {{additional_node}} into {{cuda_template_buffer.name}}, likely due to unsupported operation: {{not_implemented_op}}"
            )
            return False
        else:
            log.warning(
                f"Cannot fuse epilogue node {{additional_node}} into {{cuda_template_buffer.name}}. Reason: {{not_implemented_op}}"
            )
            return False
    return True"""  # noqa: E501
    matched_snippet = match_best_code_segment(original_file_content, snippet)
    assert matched_snippet.row_start == 50
    assert matched_snippet.row_end == 125

def test_match_best_code_segment_with_triple_quote2():
    with open(os.path.join(os.path.dirname(__file__) + '/../data/aider-cuda_cpp_scheduling.py.dt'), 'r') as f:
        original_file_content = f.read()
    snippet = """def _can_fuse_epilogue_impl(
    self,
    cuda_template_buffer: CUDATemplateBuffer,
    epilogue_nodes: List[ir.IRNode],
    additional_node: ir.IRNode,
) -> bool:
    \"\"\"
    Check if the given node can be fused with the epilogue. At the moment, Kernels
    support fusion with Pointwise operations, wrapped in (named) ComputedBuffer nodes.

    Args:
        cuda_template_buffer : A CUDATemplateBuffer object representing the CUDA template and it's result buffer
        epilogue_nodes : List[ir.Buffer]: The list of already fused epilogue nodes.
        additional_node: The ir.Buffer node to be checked if it can be fused with the epilogue.
    Returns:
    - bool: True if the given node can be fused with the epilogue, False otherwise.

    \"\"\"
    if not isinstance(cuda_template_buffer, CUDATemplateBuffer):
    ...
            log.warning(
                f"Cannot fuse epilogue node {{additional_node}} into {{cuda_template_buffer.name}}. Reason: {{not_implemented_op}}"  # noqa: G004, B950
            )
            return False
    return True"""  # noqa: E501
    matched_snippet = match_best_code_segment(original_file_content, snippet)
    assert matched_snippet.row_start == 50
    assert matched_snippet.row_end == 125

def test_match_best_code_segment_html():
    with open(os.path.join(os.path.dirname(__file__) + '/../data/express_blog-index.html.dt'), 'r') as f:
        original_file_content = f.read()
    snippet = """<body>
    <div id="app">
      <header class="header">
        <div class="header-left">
          Logo
        </div>
        <div class="header-right">
          <el-button type="primary" @click="signupVisible = true">登录</el-button>
        </div>
      </header>

      <div v-loading="loading" style="width: 80vw;height: 80vh;margin: auto;">
        <el-card v-for="item in list" class="blog-box">
          <template #header>
            <div class="list-header">
              <span>{{{{item.title}}}}</span>
              <span>{{{{item.author}}}}</span>
            </div>
          </template>
          <div>
            {{{{item.content}}}}
          </div>
        </el-card>
      </div>
      <el-dialog v-model="signupVisible" title="登录">
        <el-form ref="signupFormRef" :model="signupForm" :rules="signupFormRules">
          ..."""  # noqa: E501
    matched_snippet = match_best_code_segment(original_file_content, snippet)
    assert matched_snippet.row_start == 14
    assert matched_snippet.row_end == 39

@pytest.mark.asyncio
async def test_check_syntax():
    code = """
def test():
    print('Hello, world!')
    """
    errors = await check_syntax(code, 'py')
    assert errors == []
