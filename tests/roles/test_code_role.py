import pytest
import uuid
from typing import Iterable
import litellm

from heracles.agent_roles.role_base import RoleBase
from heracles.agent_roles.role_action import AgentRoleTaskActionFinishAction
from heracles.core.schema import EditedFileSnippet, FileContent
from heracles.core.exceptions import IDEServerFileNotFoundError, IDEServerFileBinaryError, \
    IDEServerFunCallFailureException, AgentRunException
from heracles.core.schema.task import Task, ActionType, TaskAction, TaskStep, FileActionObject, CommandActionObject, CommandLifetimeType
from heracles.agent_roles.code_role import CodeRole
from heracles.core.schema.models import CommandResultModel, TerminalInteractResponseModel


def create_task() -> Task:
    return Task(
        title='Test Task',
        description='Test Description',
        task_steps=[
            TaskStep(
                title='Test Step',
                task_actions=[
                    TaskAction(
                        title='Test Action 1',
                        action=ActionType.ADD_FILE,
                        action_object=FileActionObject(
                            path='test/path1',
                            detailed_requirement='Test requirement 1'
                        )
                    )
                ]
            )
        ]
    )

def mock_llm_send(mocker):
    async def success_result(*args, **kwargs):
        if kwargs.get('response_model') == FileContent:
            return FileContent(
                content='edited content'
            )
        elif kwargs.get('response_model') == Iterable[EditedFileSnippet]:
            return [
                EditedFileSnippet(
                    thought='change content',
                    original='search content\n' + f"{'a' * 100000}",
                    edited=f"{'b' * 100000}"
                )
            ]
        elif kwargs.get("chunk_callback") is not None:
            await kwargs["chunk_callback"]('edited content')
            return '```\nedited content\n```'
        else:
            return '```\nedited content\n```'
    mocker.patch.object(RoleBase, 'aask', side_effect=success_result)

def mock_ide_server_methods(mocker, workspace):
    file_contents: dict = {}
    snapshots: list[dict] = []

    def mock_agent_snapshot_file(*args, **kwargs):
        unique_id = str(uuid.uuid4())
        snapshots.append({'uuid': unique_id, 'path': args[0], 'value': {'content': args[1]}})
        return unique_id
    mocker.patch.object(
        workspace.playground.ide_server_client,
        'agent_snapshot_file',
        side_effect=mock_agent_snapshot_file
    )

    def mock_agent_query_snapshot_file(*args, **kwargs):
        result = []
        for snapshot in snapshots:
            if snapshot['path'] == args[0] and snapshot['uuid'] == args[1]:
                result.append(snapshot)
        return result

    mocker.patch.object(
        workspace.playground.ide_server_client,
        'agent_query_snapshot_file',
        side_effect=mock_agent_query_snapshot_file
    )

    def mock_agent_read_file(path, *args, **kwargs):
        if path in file_contents:
            return file_contents[path]
        elif path not in ['binary_or_large_file', 'not_found_file', 'large_file']:
            return 'original file content'
        elif path == 'binary_or_large_file':
            raise IDEServerFileBinaryError('File is binary or too big')
        elif path == 'not_found_file':
            raise IDEServerFileNotFoundError('File not found')
        else:
            return f"{'a' * 100000}" + '\n' + "search content" + '\n' + f"{'a' * 100000}"
    mocker.patch.object(
        workspace.playground.ide_server_client,
        'agent_read_file',
        side_effect=mock_agent_read_file
    )

    def mock_agent_write_file(path, content, *args, **kwargs):
        file_contents[path] = content
    mocker.patch.object(
        workspace.playground.ide_server_client,
        'agent_write_file',
        side_effect=mock_agent_write_file
    )

    def mock_agent_append_file(path, content, *args, **kwargs):
        if path not in file_contents:
            file_contents[path] = ''
        file_contents[path] += content

    mocker.patch.object(
        workspace.playground.ide_server_client,
        'agent_append_file',
        side_effect=mock_agent_append_file
    )

    def mock_agent_create_file(path, *args, **kwargs):
        file_contents[path] = ''
        return {'path': path, 'status': 'success'}
    mocker.patch.object(
        workspace.playground.ide_server_client,
        'agent_create_file',
        side_effect=mock_agent_create_file
    )
    mocker.patch.object(
        workspace.playground.ide_server_client,
        'agent_delete_file',
        side_effect=lambda *args, **kwargs: (
            {'status': False, 'message': 'Path does not exist!'} if 'not_found_file' in args else
            {'status': True, 'message': 'success'}
        )
    )
    mocker.patch.object(
        workspace.playground.ide_server_client,
        'agent_create_directory',
        side_effect=lambda *args, **kwargs: (
            {'status': False, 'message': 'Destination already existed'} if 'existed_directory' in args else
            {'status': True, 'message': 'success'}
        )
    )
    mocker.patch.object(
        workspace.playground.ide_server_client,
        'agent_delete_directory',
        side_effect=lambda *args, **kwargs: (
            {'status': False, 'message': 'Path does not exist!'} if 'not_found_directory' in args else
            {'status': True, 'message': 'success'}
        )
    )
    mocker.patch.object(
        workspace.playground.ide_server_client,
        'agent_move_file',
        side_effect=lambda *args, **kwargs: (
            {'status': False, 'message': 'dest path existed'} if 'existed_file' in args else
            {'status': True, 'message': 'success'}
        )
    )
    mocker.patch.object(
        workspace.playground.ide_server_client,
        'agent_move_directory',
        side_effect=lambda *args, **kwargs: (
            {'status': False, 'message': 'Path does not exist!'} if 'not_found_directory' in args else
            {'status': False, 'message': 'dest path existed'} if 'existed_directory' in args else
            {'status': True, 'message': 'success'}
        )
    )

    def mock_agent_terminal_with_result(*args, **kwargs):
        if 'wrong command' in args:
            raise IDEServerFunCallFailureException('Cmd execute fail')
        else:
            return 'command result'
    mocker.patch.object(
        workspace.playground.ide_server_client,
        'agent_terminal_with_result',
        side_effect=mock_agent_terminal_with_result
    )

@pytest.mark.asyncio
async def test_code_role_file_addition(create_workspace, mocker):
    workspace = await create_workspace
    task = create_task()
    code_role = CodeRole(workspace)

    mock_llm_send(mocker)
    mock_ide_server_methods(mocker, workspace)

    # 文件添加操作测试
    result = await code_role.run(task.task_steps[0].task_actions[0])
    assert isinstance(result, AgentRoleTaskActionFinishAction)

@pytest.mark.asyncio
async def test_code_role_file_modification_without_task(create_workspace, mocker):
    workspace = await create_workspace
    code_role = CodeRole(workspace)

    mock_llm_send(mocker)
    mock_ide_server_methods(mocker, workspace)

    # 文件修改操作正常测试
    file_action = TaskAction(
        id='1-1',
        title='Test Action 1',
        action=ActionType.MODIFY_FILE,
        action_object=FileActionObject(
            path='large_file',
            detailed_requirement='Test requirement 1',
            references=['file://large_file', 'file://other_file', 'file://not_found_file', 'playbook://not-found-playbook', 'playbook://Handling .gitignore File', '# Other references\nContent']  # noqa
        )
    )
    result = await code_role.run(file_action)
    assert isinstance(result, AgentRoleTaskActionFinishAction)

    # 文件不存在的测试
    file_action = TaskAction(
        title='Test Action 1',
        action=ActionType.MODIFY_FILE,
        action_object=FileActionObject(
            path='not_found_file',
            detailed_requirement='Test requirement 1'
        )
    )
    # 文件不存在会自动创建并修改为 ADD_FILE 类型
    res = await code_role.run(file_action)
    assert isinstance(res, AgentRoleTaskActionFinishAction)

@pytest.mark.asyncio
async def test_code_role_file_modification_failure(create_workspace, mocker):
    workspace = await create_workspace
    workspace.set_task(create_task())
    code_role = CodeRole(workspace)

    mock_ide_server_methods(mocker, workspace)

    # 文件片段匹配数量失败的测试
    file_action = TaskAction(
        title='Test Action 1',
        action=ActionType.MODIFY_FILE,
        action_object=FileActionObject(
            path='large_file.py',
            detailed_requirement='Test requirement 1'
        )
    )
    mock_logger_error = mocker.patch.object(code_role.logger, 'warning')

    def side_effect(*args, **kwargs):
        raise litellm.Timeout(message='Timeout', model='claude-test', llm_provider='openai')
    mocker.patch.object(code_role, 'aask', side_effect=side_effect)

    with pytest.raises(litellm.Timeout):
        await code_role.run(file_action)
    mock_logger_error.assert_called_with('Full file solution timed out for large_file.py')

@pytest.mark.asyncio
async def test_code_role_add_not_found_file(create_workspace, mocker):
    workspace = await create_workspace
    workspace.set_task(create_task())
    code_role = CodeRole(workspace)
    # 文件不存在会自动创建并工作
    file_action = TaskAction(
        title='Test Action 1',
        action=ActionType.ADD_FILE,
        action_object=FileActionObject(
            path='not_found_file',
            detailed_requirement='Test requirement 1'
        )
    )
    mock_llm_send(mocker)
    mock_ide_server_methods(mocker, workspace)

    await code_role.run(file_action)
    not_found_file_content = await workspace.tools.read_file('not_found_file', should_read_entire_file=True, with_line_numbers=False) #noqa
    assert not_found_file_content == 'edited content'

@pytest.mark.asyncio
async def test_code_role_command_success(create_workspace, mocker):
    workspace = await create_workspace
    task = create_task()
    code_role = CodeRole(workspace)

    mock_ide_server_methods(mocker, workspace)

    async def mock_aask(*args, **kwargs):
        if kwargs.get('response_model') == CommandResultModel:
            return CommandResultModel(success=True, reason="Command executed successfully")
        return "Mocked response"
    mocker.patch.object(code_role, 'aask', side_effect=mock_aask)

    # 命令运行成功测试
    command_action = TaskAction(
        title="运行命令",
        action=ActionType.RUN_COMMAND,
        action_object=CommandActionObject(
            command="npm -v",
            lifetime=CommandLifetimeType.SHORT
        )
    )
    task.add_task_action_by_task('1', command_action)

    result = await code_role.run(command_action)
    assert isinstance(result, AgentRoleTaskActionFinishAction)

    command_action = TaskAction(
        title="运行命令",
        action=ActionType.RUN_COMMAND,
        action_object=CommandActionObject(
            command="npm install",
            lifetime=CommandLifetimeType.LONG
        )
    )
    task.add_task_action_by_task('1', command_action)

    result = await code_role.run(command_action)
    assert isinstance(result, AgentRoleTaskActionFinishAction)

@pytest.mark.asyncio
async def test_code_role_command_failure(create_workspace, mocker):
    workspace = await create_workspace
    task = create_task()
    code_role = CodeRole(workspace)

    mock_llm_send(mocker)
    mock_ide_server_methods(mocker, workspace)
    async def mock_aask(*args, **kwargs):
        if kwargs.get('response_model') == CommandResultModel:
            if "wrong command" in args[0]:
                return CommandResultModel(success=False, reason="Command not found")
            else:
                return CommandResultModel(success=True, reason="Command run success")
        return "Mocked response"
    mocker.patch.object(code_role, 'aask', side_effect=mock_aask)

    # 命令运行失败测试
    command_action = TaskAction(
        title="运行命令",
        action=ActionType.RUN_COMMAND,
        action_object=CommandActionObject(
            command="wrong command",
            lifetime=CommandLifetimeType.LONG
        )
    )
    task.add_task_action_by_task('1', command_action)

    mock_logger = mocker.patch.object(code_role.workspace, 'trigger')
    await code_role.run(command_action)
    mock_logger.assert_called_with('chunk_message', 'Cool! The new command ran successfully now.\r\n')


@pytest.mark.asyncio
async def test_code_role_command_failure_exceed_max_attempts(create_workspace, mocker):
    workspace = await create_workspace
    task = create_task()
    code_role = CodeRole(workspace)

    mock_llm_send(mocker)
    mock_ide_server_methods(mocker, workspace)
    async def mock_aask(*args, **kwargs):
        if kwargs.get('response_model') == CommandResultModel:
            return CommandResultModel(success=False, reason="Command not found")
        return "Mocked response"
    mocker.patch.object(code_role, 'aask', side_effect=mock_aask)

    # 命令运行失败测试
    command_action = TaskAction(
        title="运行命令",
        action=ActionType.RUN_COMMAND,
        action_object=CommandActionObject(
            command="wrong command",
            lifetime=CommandLifetimeType.LONG
        )
    )
    task.add_task_action_by_task('1', command_action)

    with pytest.raises(AgentRunException, match='Command execution failed'):
        await code_role.run(command_action)

@pytest.mark.asyncio
async def test_code_role_other_file_actions(create_workspace, mocker):
    workspace = await create_workspace
    workspace.set_task(create_task())
    code_role = CodeRole(workspace)

    async def run_and_assert(action, expected_exception=None):
        if expected_exception:
            with pytest.raises(expected_exception):
                await code_role.run(action)
        else:
            await code_role.run(action)

    mock_ide_server_methods(mocker, workspace)

    # 文件删除测试
    file_action = TaskAction(
        title='Test Action 1',
        action=ActionType.DELETE_FILE,
        action_object=FileActionObject(
            path='test_file'
        )
    )
    await run_and_assert(file_action)
    file_action = TaskAction(
        title='Test Action 1',
        action=ActionType.DELETE_FILE,
        action_object=FileActionObject(
            path='not_found_file'
        )
    )
    await run_and_assert(file_action, AgentRunException)

    # 目录删除测试
    dir_action = TaskAction(
        title='Test Action 3',
        action=ActionType.DELETE_DIRECTORY,
        action_object=FileActionObject(
            path='existed_directory'
        )
    )
    await run_and_assert(dir_action)
    dir_action = TaskAction(
        title='Test Action 3',
        action=ActionType.DELETE_DIRECTORY,
        action_object=FileActionObject(
            path='not_found_directory'
        )
    )
    await run_and_assert(dir_action, AgentRunException)

    # 文件移动测试
    move_file_action = TaskAction(
        title='Test Action 4',
        action=ActionType.MOVE_FILE,
        action_object=FileActionObject(
            path='source_file',
            target='new_file'
        )
    )
    await run_and_assert(move_file_action)
    move_file_action = TaskAction(
        title='Test Action 4',
        action=ActionType.MOVE_FILE,
        action_object=FileActionObject(
            path='source_file',
            target='existed_file'
        )
    )
    await run_and_assert(move_file_action, AgentRunException)
    move_file_action = TaskAction(
        title='Test Action 4',
        action=ActionType.MOVE_FILE,
        action_object=FileActionObject(
            path='not_found_file',
            target='existed_file'
        )
    )
    await run_and_assert(move_file_action, AgentRunException)

    # 目录移动测试
    move_dir_action = TaskAction(
        title='Test Action 5',
        action=ActionType.MOVE_DIRECTORY,
        action_object=FileActionObject(
            path='source_directory',
            target='new_directory'
        )
    )
    await run_and_assert(move_dir_action)
    move_dir_action = TaskAction(
        title='Test Action 5',
        action=ActionType.MOVE_DIRECTORY,
        action_object=FileActionObject(
            path='source_directory',
            target='existed_directory'
        )
    )
    await run_and_assert(move_dir_action, AgentRunException)
    move_dir_action = TaskAction(
        title='Test Action 5',
        action=ActionType.MOVE_DIRECTORY,
        action_object=FileActionObject(
            path='not_found_directory',
            target='new_directory'
        )
    )
    await run_and_assert(move_dir_action, AgentRunException)

@pytest.mark.asyncio
async def test_code_role_generate_apply_solution(create_workspace, mocker):
    """
    测试 CodeRole 在 full file 方案超时后自动切换到 generate_apply 方案，
    并验证 _generate_edit_instructions 和 _apply_instructions_for_code 的调用。
    """
    workspace = await create_workspace
    code_role = CodeRole(workspace)

    # 构造一个较大文件，确保走 generate_apply 分支
    file_content = 'line\n' * 500  # 500 行
    file_action = TaskAction(
        id='1-apply',
        title='Test Action generate_apply',
        action=ActionType.MODIFY_FILE,
        action_object=FileActionObject(
            path='large_file_apply.py',
            detailed_requirement='Test requirement for generate_apply',
            references=[]
        )
    )

    # mock 相关依赖
    mock_ide_server_methods(mocker, workspace)
    # full file 方案超时
    mocker.patch.object(
        code_role, '_strong_full_file_solution',
        side_effect=litellm.Timeout(message='Timeout', model='claude-test', llm_provider='openai')
    )
    # mock _generate_edit_instructions 返回指令
    mock_generate_edit_instructions = mocker.patch.object(code_role, '_generate_edit_instructions', return_value='Edit instructions')
    # mock _apply_instructions_for_code 返回最终代码
    mock_apply_instructions_for_code = mocker.patch.object(code_role, '_apply_instructions_for_code', return_value='final code after apply')
    # mock 读文件内容
    mocker.patch.object(workspace.tools, 'read_file_content', return_value=file_content)
    # mock 写文件
    mocker.patch.object(workspace.tools, 'write_file', return_value=None)
    # mock 语法检查
    mocker.patch('heracles.agent_roles.code_role.check_syntax', return_value=[])
    # mock diff
    mocker.patch('heracles.agent_roles.code_role.generate_file_diff', return_value='diff result')

    # 执行
    result = await code_role.run(file_action)
    assert isinstance(result, AgentRoleTaskActionFinishAction)
    assert result.result == 'diff result'
    mock_generate_edit_instructions.assert_called_once()
    # 只断言后三个参数
    called_args = mock_apply_instructions_for_code.call_args[0]
    assert called_args[1] == file_action.action_object
    assert called_args[2] == 'Edit instructions'
    assert called_args[3] == file_content

@pytest.mark.asyncio
async def test_apply_instructions_for_code_real_call(create_workspace, mocker):
    """
    直接测试 _apply_instructions_for_code 方法体，mock 掉外部依赖，不 mock 同级别方法。
    """
    workspace = await create_workspace
    code_role = CodeRole(workspace)

    # 构造参数
    task_action_id = 'apply-test-id'
    action_object = FileActionObject(
        path='test.py',
        detailed_requirement='add a function',
        references=[]
    )
    instructions = 'Add a hello function'
    original = 'print("hi")\n'

    # mock aask
    mock_aask = mocker.patch.object(code_role, 'aask', return_value='new file content')

    # 调用
    result = await code_role._apply_instructions_for_code(task_action_id, action_object, instructions, original)
    assert result == 'new file content'
    mock_aask.assert_called_once()

@pytest.mark.asyncio
async def test_estimate_file_lines(create_workspace, mocker):
    """
    测试 _estimate_file_lines 方法，mock prompt 和 aask，覆盖正常和异常分支。
    """
    workspace = await create_workspace
    code_role = CodeRole(workspace)

    # 正常返回数字
    mocker.patch.object(code_role, 'aask', return_value='123')
    result = await code_role._estimate_file_lines('foo.py', 'add foo')
    assert result == 123

    # 返回非数字，触发异常分支
    mocker.patch.object(code_role, 'aask', return_value='not a number')
    result = await code_role._estimate_file_lines('foo.py', 'add foo')
    assert result == 0

@pytest.mark.asyncio
async def test_terminal_interact_needs_input(create_workspace, mocker):
    """
    测试 terminal_interact 方法，当 AI 判断需要输入时返回响应内容。
    """
    workspace = await create_workspace
    code_role = CodeRole(workspace)

    # 构造参数
    action = CommandActionObject(
        command="npm install",
        lifetime=CommandLifetimeType.LONG
    )

    # 模拟 Future 对象
    future = mocker.MagicMock()
    future._cmd = "npm install"
    future._terminal_result.to_string.return_value = "Installing packages..."

    mock_aask = mocker.patch.object(code_role, 'aask', return_value=TerminalInteractResponseModel(needs_input=True, response="y"))

    mock_prompt_builder = mocker.patch('heracles.agent_roles.code_role.PromptBuilder')
    mock_prompt_builder.return_value.format = mocker.AsyncMock(return_value="formatted prompt")

    result = await code_role.generate_terminal_interact_response(action, future)

    assert result == "y"
    mock_aask.assert_called_once()
    assert not future.set_exception.called

@pytest.mark.asyncio
async def test_terminal_interact_no_input_needed(create_workspace, mocker):
    """
    测试 terminal_interact 方法，当 AI 判断不需要输入时设置超时异常。
    """
    workspace = await create_workspace
    code_role = CodeRole(workspace)

    # 构造参数
    action = CommandActionObject(
        command="ls -la",
        lifetime=CommandLifetimeType.SHORT
    )

    # 模拟 Future 对象
    future = mocker.MagicMock()
    future._cmd = "ls -la"
    future._terminal_result.to_string.return_value = "total 0\ndrwxr-xr-x 2 <USER> <GROUP> 4096 ..."

    mock_aask = mocker.patch.object(code_role, 'aask', return_value=TerminalInteractResponseModel(needs_input=False, response="Command completed")) # noqa

    mock_prompt_builder = mocker.patch('heracles.agent_roles.code_role.PromptBuilder')
    mock_prompt_builder.return_value.format = mocker.AsyncMock(return_value="formatted prompt")

    result = await code_role.generate_terminal_interact_response(action, future)

    assert result is None
    mock_aask.assert_called_once()


@pytest.mark.asyncio
async def test_is_cmd_run_success_command_successful(create_workspace, mocker):
    """
    测试 is_cmd_run_success 方法，命令执行成功的情况。
    """
    workspace = await create_workspace
    code_role = CodeRole(workspace)

    # mock CommandResult 返回成功
    command_result = CommandResultModel(success=True, reason="Command executed successfully")

    mock_aask = mocker.patch.object(code_role, 'aask', return_value=command_result)

    # mock PromptBuilder
    mock_prompt_builder = mocker.patch('heracles.agent_roles.code_role.PromptBuilder')
    mock_prompt_builder.return_value.format = mocker.AsyncMock(return_value="formatted prompt")

    # 调用
    success, reason = await code_role.analyze_cmd_result("npm -v", "8.19.3")

    # 验证
    assert success is True
    assert reason == "Command executed successfully"
    mock_aask.assert_called_once()

@pytest.mark.asyncio
async def test_is_cmd_run_success_command_failed(create_workspace, mocker):
    """
    测试 is_cmd_run_success 方法，命令执行失败的情况。
    """
    workspace = await create_workspace
    code_role = CodeRole(workspace)

    mock_aask = mocker.patch.object(code_role, 'aask', return_value=CommandResultModel(success=False, reason="Command not found"))

    mock_prompt_builder = mocker.patch('heracles.agent_roles.code_role.PromptBuilder')
    mock_prompt_builder.return_value.format = mocker.AsyncMock(return_value="formatted prompt")

    success, reason = await code_role.analyze_cmd_result("wrong_command", "bash: wrong_command: command not found")

    assert success is False
    assert reason == "Command not found"
    mock_aask.assert_called_once()

async def run_stream_append_file_test(create_workspace, mocker, chunks, full_file_content):
    workspace = await create_workspace
    code_role = CodeRole(workspace)

    file_content = []
    async def mock_append_file(path, chunk):
        file_content.append(chunk)
    mocker.patch.object(workspace.tools, 'append_file', side_effect=mock_append_file)

    await code_role.init_stream_append_file('test.md')
    for chunk, expected_content in chunks:
        await code_role.stream_append_file(chunk)
        assert ''.join(file_content) == expected_content

    await code_role.end_stream_append_file()
    assert code_role._full_filtered_file_content == full_file_content

@pytest.mark.asyncio
async def test_stream_append_file_with_instructions(create_workspace, mocker):
    chunks = [
        ('I will edit this\n', ''),
        ('<__file_content_start__>\n', ''),
        ('Complex Content\n', 'Complex Content\n'),
        ('</__file_content_end__>\n', 'Complex Content\n'),
    ]
    full_file_content = 'Complex Content\n'

    await run_stream_append_file_test(create_workspace, mocker, chunks, full_file_content)

@pytest.mark.asyncio
async def test_stream_append_file_split_markers(create_workspace, mocker):
    """Test when start/end markers are split across multiple chunks"""
    chunks = [
        ('<__file_content_start__>\n', ''),
        ('Hello\n', 'Hello\n'),
        ('World\n', 'Hello\nWorld\n'),
        ('</__file_content_end__>\n', 'Hello\nWorld\n'),
    ]
    full_file_content = 'Hello\nWorld\n'

    await run_stream_append_file_test(create_workspace, mocker, chunks, full_file_content)

@pytest.mark.asyncio
async def test_stream_append_file_single_char_chunks(create_workspace, mocker):
    """Test when content comes in single character chunks"""
    chunks = [
        ('<__file_content_', ''),
        ('start__>\n', ''),
        ('H', ''),
        ('e', ''),
        ('l', ''),
        ('l', ''),
        ('o', ''),
        ('\n', 'Hello\n'),
        ('</__file_content_', 'Hello\n'),
        ('end__>\n', 'Hello\n'),
    ]
    full_file_content = 'Hello\n'

    await run_stream_append_file_test(create_workspace, mocker, chunks, full_file_content)

@pytest.mark.asyncio
async def test_stream_append_file_empty_content(create_workspace, mocker):
    """Test when there's no content between markers"""
    chunks = [
        ('<__file_content_', ''),
        ('start__>\n', ''),
        ('</__file_content_', ''),
        ('end__>\n', ''),
    ]
    full_file_content = ''

    await run_stream_append_file_test(create_workspace, mocker, chunks, full_file_content)

@pytest.mark.asyncio
async def test_stream_append_file_multiline_content(create_workspace, mocker):
    """Test multiline content with various whitespace"""
    chunks = [
        ('<__file_content_', ''),
        ('start__>\n', ''),
        ('  def hello()', ''),
        (':\n', '  def hello():\n'),
        ('    print("', '  def hello():\n'),
        ('world")\n', '  def hello():\n    print("world")\n'),
        ('  \n', '  def hello():\n    print("world")\n  \n'),
        ('</__file_content_', '  def hello():\n    print("world")\n  \n'),
        ('end__>\n', '  def hello():\n    print("world")\n  \n'),
    ]
    full_file_content = '  def hello():\n    print("world")\n  \n'

    await run_stream_append_file_test(create_workspace, mocker, chunks, full_file_content)

@pytest.mark.asyncio
async def test_stream_append_file_without_markers(create_workspace, mocker):
    """Test without markers"""
    chunks = [
        ('  def hello():\n', ''),
        ('    print("world")\n', ''),
        ('  \n', ''),
    ]
    full_file_content = '  def hello():\n    print("world")\n  \n'

    await run_stream_append_file_test(create_workspace, mocker, chunks, full_file_content)
