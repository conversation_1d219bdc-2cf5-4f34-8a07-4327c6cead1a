import pytest
import textwrap
import os
from unittest.mock import <PERSON><PERSON><PERSON>, AsyncMock
from heracles.agent_controller.llm import LLM
from heracles.core.schema import AdminDataResponse
from heracles.agent_workspace.playbook import Playbook
from heracles.core.utils.git_message_parser import _parse_git_file_change_message_no_rename
from heracles.core.exceptions import Agent<PERSON><PERSON>Ex<PERSON>, IDEServerFunCallException
from heracles.agent_roles.utils import extract_referenced_contents
from heracles.core.utils import DotDict

@pytest.mark.asyncio
async def test_workspace_rag(create_workspace, mocker):
    workspace = await create_workspace

    mocker.patch.object(LLM, 'async_send', return_value='hello result')
    rag_searcher = workspace.rag_searcher
    await rag_searcher.search('hello')

def test_workspace_git():
    _test_message = """
    A       1
    A       2
    D       config.ru
    A       config.ru1
    """
    test_message = textwrap.dedent(_test_message)
    file_change_list = _parse_git_file_change_message_no_rename(test_message)
    assert len(file_change_list), 4

    _test_message = """
    ?? 123
     M .gitignore
    M\t.1024
    """
    test_message = textwrap.dedent(_test_message)
    file_change_list = _parse_git_file_change_message_no_rename(test_message)
    assert file_change_list[0].status == 'U'
    assert file_change_list[0].path == '123'
    assert file_change_list[1].status == 'M'
    assert file_change_list[1].path == '.gitignore'

    _test_message = ""
    test_message = textwrap.dedent(_test_message)

    file_change_list = _parse_git_file_change_message_no_rename(test_message)
    assert file_change_list == []

    _test_message = "wrongline"
    test_message = textwrap.dedent(_test_message)
    with pytest.raises(AgentRunException):
        file_change_list = _parse_git_file_change_message_no_rename(test_message)

    # 测试 \t 这种情况
    _test_message = """
    A \ttab1
    """
    test_message = textwrap.dedent(_test_message)
    file_change_list = _parse_git_file_change_message_no_rename(test_message)
    assert file_change_list[0].status == 'A'
    assert file_change_list[0].path == 'tab1'

    _test_message = """
    A \t "中文 123.txt"
    A \ttab1
    A       123.txt
    A       "\344\270\255\346\226\207 123.txt"
     A      "\344\270\255\346\226\207.txt"
    """
    test_message = textwrap.dedent(_test_message)
    file_change_list = _parse_git_file_change_message_no_rename(test_message)
    assert file_change_list[0].path == "中文 123.txt"
    assert file_change_list[0].status == "A"
    assert file_change_list[-1].path == "中文.txt"
    assert file_change_list[-1].status == "A"

    _test_message = """
    AM\t "中文 123.txt"
    M \t "中文.txt"
    """
    test_message = textwrap.dedent(_test_message)
    file_change_list = _parse_git_file_change_message_no_rename(test_message)
    assert file_change_list[0].path == "中文 123.txt"
    assert file_change_list[0].status == "M"
    assert file_change_list[-1].path == "中文.txt"
    assert file_change_list[-1].status == "M"

@pytest.mark.asyncio
async def test_workspace_file_tree_simple(mocker, create_workspace):
    workspace = await create_workspace

    mocker.patch.object(
        workspace.playground.ide_server_client,
        'agent_file_tree',
        return_value={'type': 'DIRECTORY', 'name': '.', 'path': '.', 'children': [{'type': 'FILE', 'name': '.1024', 'path': '.1024', 'children': []}, {'type': 'FILE', 'name': '.1024nix', 'path': '.1024nix', 'children': []}, {'type': 'FILE', 'name': '.gitignore', 'path': '.gitignore', 'children': []}, {'type': 'FILE', 'name': 'README.md', 'path': 'README.md', 'children': []}, {'type': 'FILE', 'name': 'go-blog', 'path': 'go-blog', 'children': []}, {'type': 'FILE', 'name': 'go.mod', 'path': 'go.mod', 'children': []}, {'type': 'FILE', 'name': 'go.sum', 'path': 'go.sum', 'children': []}, {'type': 'FILE', 'name': 'go1.19.linux-amd64.tar.gz', 'path': 'go1.19.linux-amd64.tar.gz', 'children': []}, {'type': 'FILE', 'name': 'handlers.article.go', 'path': 'handlers.article.go', 'children': []}, {'type': 'FILE', 'name': 'handlers.user.go', 'path': 'handlers.user.go', 'children': []}, {'type': 'FILE', 'name': 'main.go', 'path': 'main.go', 'children': []}, {'type': 'FILE', 'name': 'models.article.go', 'path': 'models.article.go', 'children': []}, {'type': 'FILE', 'name': 'models.user.go', 'path': 'models.user.go', 'children': []}, {'type': 'DIRECTORY', 'name': 'public', 'path': 'public', 'children': [{'type': 'DIRECTORY', 'name': 'uploads', 'path': 'public/uploads', 'children': [{'type': 'FILE', 'name': 'cat-1.jpg', 'path': 'public/uploads/cat-1.jpg', 'children': []}, {'type': 'FILE', 'name': 'cat-2.jpg', 'path': 'public/uploads/cat-2.jpg', 'children': []}]}]}, {'type': 'FILE', 'name': 'routes.go', 'path': 'routes.go', 'children': []}, {'type': 'DIRECTORY', 'name': 'templates', 'path': 'templates', 'children': [{'type': 'FILE', 'name': 'article.html', 'path': 'templates/article.html', 'children': []}, {'type': 'FILE', 'name': 'footer.html', 'path': 'templates/footer.html', 'children': []}, {'type': 'FILE', 'name': 'header.html', 'path': 'templates/header.html', 'children': []}, {'type': 'FILE', 'name': 'index.html', 'path': 'templates/index.html', 'children': []}, {'type': 'FILE', 'name': 'menu.html', 'path': 'templates/menu.html', 'children': []}, {'type': 'FILE', 'name': 'new_article.html', 'path': 'templates/new_article.html', 'children': []}, {'type': 'FILE', 'name': 'register.html', 'path': 'templates/register.html', 'children': []}]}]} # noqa
    )
    file_tree = await workspace.tools.file_tree()
    assert '.1024' in file_tree

    file_tree = await workspace.tools.file_tree(show_dirs=True)
    assert 'public/uploads/' in file_tree

    res = await workspace.tools.check_path_exists('.1024')
    assert res

@pytest.mark.asyncio
async def test_workspace_file_tree_complex(mocker, create_workspace):
    workspace = await create_workspace
    with open('tests/data/large_agent_file_tree.dt', 'r', encoding='utf-8') as f:
        import ast
        mock_file_tree = ast.literal_eval(f.read())

    mocker.patch.object(
        workspace.playground.ide_server_client,
        'agent_file_tree',
        return_value=mock_file_tree
    )
    file_tree = await workspace.tools.file_tree()
    assert 'WARN: node_modules/@anthropic-ai/sdk/_shims/... more files omitted in this level' in file_tree
    assert '\nWARN: ...Max files (1000) reached...' in file_tree

@pytest.mark.asyncio
async def test_workspace_read_file(mocker, create_workspace):
    workspace = await create_workspace
    mocker.patch.object(
        workspace.playground.ide_server_client,
        'agent_read_file',
        return_value=f"{'x' * 20}"
    )
    res = await workspace.tools.read_file('test', should_read_entire_file=True, with_line_numbers=False)
    res = await workspace.tools.read_file('test', should_read_entire_file=True, with_line_numbers=False)
    assert f"{'x' * 20}" == res

    mocker.patch.object(
        workspace.playground.ide_server_client,
        'agent_read_file',
        return_value="\n".join(["x"]*22000)
    )
    res = await workspace.tools.read_file('test', start_line=1, end_line=20)
    assert f'... {22000 - 150} lines after ...' in res

@pytest.mark.asyncio
async def test_workspace_create_file(mocker, create_workspace):
    workspace = await create_workspace

    mocker.patch.object(
        workspace.playground.ide_server_client,
        '_agent_func_call',
        return_value={'status': False, 'message': 'File already exists'}
        # return_value={'status': False, 'message': 'reach max count'}
    )
    with pytest.raises(AgentRunException):
        await workspace.tools.create_file('test')

    mocker.patch.object(
        workspace.playground.ide_server_client,
        'agent_create_file',
        return_value={'status': True, 'message': 'success'}
    )
    res = await workspace.tools.create_file('test')
    assert res

@pytest.mark.asyncio
async def test_workspace_write_file(mocker, create_workspace):
    workspace = await create_workspace

    mocker.patch.object(
        workspace.playground.ide_server_client,
        'agent_write_file',
        side_effect=IDEServerFunCallException('func_call agent_write_file fatal: [FileTree_readFile]EISDIR: illegal operation on a directory, read')  # noqa
    )
    with pytest.raises(IDEServerFunCallException, match='EISDIR'):
        await workspace.tools.write_file('abc/abc', 'test content')

    mocker.patch.object(
        workspace.playground.ide_server_client,
        'agent_write_file',
        side_effect=IDEServerFunCallException("func_call agent_write_file fatal: [FileTree_readFile]ENOENT: no such file or directory, open '/app/data/codeZone/2024/1/9-18/@xxx/source/abc/abc/test'") # noqa
    )
    with pytest.raises(IDEServerFunCallException, match='ENOENT'):
        await workspace.tools.write_file('abc/abc/test', 'test content')

    mocker.patch.object(
        workspace.playground.ide_server_client,
        'agent_write_file',
        return_value=None
    )
    res = await workspace.tools.write_file('test.md', 'test content')
    assert res

@pytest.mark.asyncio
async def test_workspace_move_file(mocker, create_workspace):
    workspace = await create_workspace

    mocker.patch.object(
        workspace.playground.ide_server_client,
        '_agent_func_call',
        return_value={'status': False, 'message': 'dest path existed'}
    )
    with pytest.raises(AgentRunException):
        await workspace.tools.move_file('README_new.md', 'README.md')

    # 文件存在，目录存在，直接成功
    mocker.patch.object(
        workspace.playground.ide_server_client,
        'agent_move_file',
        return_value={'status': True, 'message': 'success'}
    )
    res = await workspace.tools.move_file('README.MD', 'README.md')
    assert res

@pytest.mark.asyncio
async def test_workspace_move_file_not_found(mocker, create_workspace):
    workspace = await create_workspace
    # 文件不存在，直接返回False
    mocker.patch.object(
        workspace.playground.ide_server_client,
        '_agent_func_call',
        return_value={'status': False, 'message': "ENOENT: no such file or directory, rename '.../README.MD' -> '.../README.md'"}
    )
    with pytest.raises(AgentRunException):
        await workspace.tools.move_file('README.MD', 'README.md')

@pytest.mark.asyncio
async def test_workspace_delete_file(mocker, create_workspace):
    workspace = await create_workspace

    mocker.patch.object(
        workspace.playground.ide_server_client,
        '_agent_func_call',
        return_value={'status': False, 'message': 'Path does not exist!'}
    )
    with pytest.raises(AgentRunException):
        await workspace.tools.delete_file('test')

    mocker.patch.object(
        workspace.playground.ide_server_client,
        'agent_delete_file',
        return_value={'status': True, 'message': 'success'}
    )
    res = await workspace.tools.delete_file('test')
    assert res

@pytest.mark.asyncio
async def test_workspace_create_directory(mocker, create_workspace):
    workspace = await create_workspace

    mocker.patch.object(
        workspace.playground.ide_server_client,
        '_agent_func_call',
        return_value={'status': False, 'message': 'Destination already existed'}
    )
    with pytest.raises(AgentRunException):
        await workspace.tools.create_directory('test')

    mocker.patch.object(
        workspace.playground.ide_server_client,
        'agent_create_directory',
        return_value={'status': True, 'message': 'success'}
    )
    res = await workspace.tools.create_directory('test/test2/test3')
    assert res

@pytest.mark.asyncio
async def test_workspace_move_directory(mocker, create_workspace):
    workspace = await create_workspace

    # 目录存在，目标目录存在，直接成功
    mocker.patch.object(
        workspace.playground.ide_server_client,
        'agent_move_directory',
        return_value={'status': True, 'message': 'success'}
    )
    res = await workspace.tools.move_directory('abcd', 'abc')
    assert res

    # 目标目录已经存在，直接返回False
    mocker.patch.object(
        workspace.playground.ide_server_client,
        'agent_move_directory',
        return_value={'status': False, 'message': 'dest path existed'}
    )
    with pytest.raises(AgentRunException, match='dest path existed'):
        await workspace.tools.move_directory('abcd', 'abcd')

@pytest.mark.asyncio
async def test_workspace_move_directory_not_found(mocker, create_workspace):
    workspace = await create_workspace
    mocker.patch.object(
        workspace.playground.ide_server_client,
        '_agent_func_call',
        return_value={'status': False, 'message': "ENOENT: no such file or directory, rename '.../test/test2' -> '.../test/test3'"}
    )
    with pytest.raises(AgentRunException):
        await workspace.tools.move_directory('test/test2', 'test/test3')

@pytest.mark.asyncio
async def test_workspace_delete_directory(mocker, create_workspace):
    workspace = await create_workspace

    mocker.patch.object(
        workspace.playground.ide_server_client,
        '_agent_func_call',
        return_value={'status': False, 'message': 'Path does not exist!'}
    )
    with pytest.raises(AgentRunException):
        await workspace.tools.delete_directory('test')

    mocker.patch.object(
        workspace.playground.ide_server_client,
        'agent_delete_directory',
        return_value={'status': True, 'message': 'success'}
    )
    res = await workspace.tools.delete_directory('test')
    assert res

@pytest.mark.asyncio
async def test_workspace_read_playbook(create_workspace):
    workspace = await create_workspace

    playbooks = workspace.playbook_manager.system_playbooks
    first_playbook = playbooks[0]
    playbook = await workspace.tools.read_playbook(first_playbook.id)
    assert first_playbook.content in playbook

    with pytest.raises(AgentRunException, match='Playbook not found, id: test'):
        await workspace.tools.read_playbook('test')

@pytest.mark.asyncio
async def test_workspace_search_system_playbook(create_workspace):
    workspace = await create_workspace

    playbooks = workspace.playbook_manager.search_by_tags(tags=['initialization'])
    assert len(playbooks) > 0

@pytest.mark.asyncio
async def test_workspace_playbook_manager(mocker, create_workspace):
    workspace = await create_workspace

    mock_response = AdminDataResponse.model_validate({
        "status": "ok",
        "statusCode": 200,
        "error_msg": "",
        "data": [
            {
                "id": "9bbf91ea-043b-44d9-a043-fa5a615cb110",
                "title": "title",
                "description": "",
                "tags": [
                  "initialization"
                ],
                "original_content": "original_content",
                "source": "user",
                "status": "ready"
            }
        ]
    })
    mocker.patch.dict(os.environ, {"ADMIN_VECTOR_DB_HOST_OPTIONAL": "for-test"})
    mocker.patch('heracles.agent_workspace.playbook_manager.query_playbook', return_value=mock_response)
    mocker.patch('heracles.agent_workspace.playbook_manager.create_playbook', return_value=mock_response)
    query_result = await workspace.playbook_manager.search_playbook_from_db('original_content', ['initialization'])
    assert query_result
    assert isinstance(query_result[0], Playbook)
    assert workspace.playbook_manager.get('title')

    playbook = Playbook(title='', tags=[], applicable_rules=[], original_content='', status='pending')
    await workspace.playbook_manager.upload(playbook)

@pytest.mark.asyncio
async def test_workspace_tools_snapshot_file(mocker, create_workspace):
    workspace = await create_workspace

    mock_uuid = "test-uuid-123"
    mocker.patch.object(workspace.tools.playground, 'func_call', return_value=mock_uuid)

    uuid = await workspace.tools.snapshot_file("test.py", "test content")
    assert uuid == mock_uuid

@pytest.mark.asyncio
async def test_workspace_tools_query_snapshot_file(mocker, create_workspace):
    workspace = await create_workspace

    mock_snapshots = [
        {
            "value": {
                "content": "test content 1"
            }
        },
        {
            "value": {
                "content": "test content 2"
            }
        }
    ]
    mocker.patch.object(workspace.tools.playground, 'func_call', return_value=mock_snapshots)

    snapshots = await workspace.tools.query_snapshot_file("test.py")
    assert len(snapshots) == 2
    assert snapshots[0]["value"]["content"] == "test content 1"
    assert snapshots[1]["value"]["content"] == "test content 2"

@pytest.mark.asyncio
async def test_workspace_tools_query_snapshot_file_by_uuid(mocker, create_workspace):
    workspace = await create_workspace

    mock_snapshots = [
        {
            "value": {
                "content": "test content"
            }
        }
    ]
    mocker.patch.object(workspace.tools.playground, 'func_call', return_value=mock_snapshots)

    content = await workspace.tools.query_snapshot_file_by_uuid("test.py", "test-uuid")
    assert content == "test content"

@pytest.mark.asyncio
async def test_workspace_tools_query_snapshot_file_by_uuid_not_found(mocker, create_workspace):
    workspace = await create_workspace

    mocker.patch.object(workspace.tools.playground, 'func_call', return_value=[])

    with pytest.raises(AgentRunException) as exc:
        await workspace.tools.query_snapshot_file_by_uuid("test.py", "test-uuid")
    assert "File snapshot not found" in str(exc.value)

@pytest.mark.asyncio
async def test_workspace_tools_run_project(mocker, create_workspace):
    workspace = await create_workspace

    mock_result = {"status": "success"}
    mocker.patch.object(workspace.tools.playground, 'func_call', return_value=mock_result)

    result = await workspace.tools.run_project()
    assert result == mock_result

@pytest.mark.asyncio
async def test_workspace_tools_stop_project(mocker, create_workspace):
    workspace = await create_workspace

    mock_result = {"status": "success"}
    mocker.patch.object(workspace.tools.playground, 'func_call', return_value=mock_result)

    result = await workspace.tools.stop_project()
    assert result == mock_result

@pytest.mark.asyncio
async def test_workspace_tools_local_url(mocker, create_workspace):
    workspace = await create_workspace

    mock_http_ports = [{"host": "localhost", "port": 8080}]
    mocker.patch.object(workspace.tools.playground.ide_server_client, 'http_ports', mock_http_ports)

    url = await workspace.tools.local_url()
    assert url == "http://localhost:8080"

@pytest.mark.asyncio
async def test_workspace_tools_local_url_no_ports(mocker, create_workspace):
    workspace = await create_workspace

    mocker.patch.object(workspace.tools.playground.ide_server_client, 'http_ports', [])

    url = await workspace.tools.local_url()
    assert url is None

@pytest.mark.asyncio
async def test_workspace_tools_list_middlewares(mocker, create_workspace):
    workspace = await create_workspace

    mock_playground_info = {
        'data': {
            'codeZoneId': 'test-id'
        }
    }
    mock_middlewares = {
        'data': [
            {
                'name': 'mysql',
                'innerEnvMap': {'key': 'value'}
            }
        ]
    }
    mocker.patch('heracles.agent_workspace.tools.bind_playground_info', return_value=mock_playground_info)
    mocker.patch('heracles.agent_workspace.tools.list_middlewares_of_codezone', return_value=mock_middlewares)

    result = await workspace.tools.list_bound_middlewares_in_environment()
    assert len(result) == 1
    assert result[0]['name'] == 'mysql'
    assert result[0]['connect_info'] == {'key': 'value'}

@pytest.mark.asyncio
async def test_workspace_tools_middlewares_not_empty(mocker, create_workspace):
    workspace = await create_workspace

    mock_middlewares = [{
        'name': 'test-middleware',
        'connect_info': {'key': 'value'}
    }]
    mocker.patch.object(workspace.tools, 'list_bound_middlewares_in_environment', return_value=mock_middlewares)

    result = await workspace.tools.middlewares_not_empty()
    assert result is True

    mocker.patch.object(workspace.tools, 'list_bound_middlewares_in_environment', return_value=[])
    result = await workspace.tools.middlewares_not_empty()
    assert result is False

@pytest.mark.asyncio
async def test_workspace_extract_referenced_contents(mocker, create_workspace):
    workspace = await create_workspace
    mock_response = mocker.MagicMock()
    mock_response.status = 200

    mock_session = mocker.MagicMock()
    mock_session.__aenter__.return_value = mock_session
    mock_session.__aexit__.return_value = None
    mock_session.get.return_value.__aenter__.return_value = mock_response
    mocker.patch('aiohttp.ClientSession', return_value=mock_session)
    references = [
        'image_url://http://test.com/test0.svg',
        'image_url://http://test.com/test1.jpg',
        'image_url://https://test.com/test2.png',
        'image_url://test3.jpg',
        'image_url://xxx/test4.png',
        'image_url://file://test5.webp',
        'image_url://file:////test6.gif',
        'image_url://ftp://test.com/test7.webp',
        'image_url://xxx://test.com/test8.gif',
        'image_url://http://test.com/test/test/test8.gif',
        'image_url://http://test.com/test9.webp',
        'image_url://http://test.com/test9.webp',
        'image_url://https://backend-staging-qwertyuiopas3456.s3-accelerate.amazonaws.com/images/2025/06/08/01920a1e-5545-76d0-b3e3-09df42e2e8ef/screenshot_17493811343291749381134491'
    ]
    res = await extract_referenced_contents(references, workspace)
    expected = [
        'http://test.com/test1.jpg',
        'https://test.com/test2.png',
        'http://test.com/test/test/test8.gif',
        'http://test.com/test9.webp',
        'https://backend-staging-qwertyuiopas3456.s3-accelerate.amazonaws.com/images/2025/06/08/01920a1e-5545-76d0-b3e3-09df42e2e8ef/screenshot_17493811343291749381134491'
    ]

    assert set(res['images']) == set(expected)

@pytest.mark.asyncio
async def test_workspace_file_tree_with_tips_nodes(mocker, create_workspace):
    """测试文件树中包含TIPS节点的处理（35, 70行）"""
    workspace = await create_workspace

    # 模拟包含TIPS节点的文件树
    mock_file_tree = {
        'type': 'DIRECTORY',
        'name': '.',
        'path': '',
        'children': [
            {
                'type': 'DIRECTORY',
                'name': 'src',
                'path': 'src',
                'children': [
                    {
                        'type': 'TIPS',
                        'name': 'Hidden directory not show...',
                        'path': '',
                        'children': []
                    }
                ]
            },
            {
                'type': 'DIRECTORY',
                'name': 'dist',
                'path': 'dist',
                'children': [
                    {
                        'type': 'TIPS',
                        'name': 'Max 30 items per level...',
                        'path': '',
                        'children': []
                    }
                ]
            },
            {
                'type': 'TIPS',
                'name': 'Max files (1000) reached...',
                'path': '',
                'children': []
            }
        ]
    }

    mocker.patch.object(
        workspace.playground.ide_server_client,
        'agent_file_tree',
        return_value=mock_file_tree
    )

    # 测试show_dirs=True时的TIPS处理
    file_tree = await workspace.tools.file_tree(show_dirs=True)
    file_tree_str = '\n'.join(file_tree) if isinstance(file_tree, list) else str(file_tree)

    assert 'src/: hidden directory' in file_tree_str
    assert 'WARN: dist/... more files omitted in this level' in file_tree_str
    assert '...Max files (1000) reached...' in file_tree_str

@pytest.mark.asyncio
async def test_workspace_run_cmd_with_soft_callback(mocker, create_workspace):
    """测试带软超时回调的命令执行（185行）"""
    workspace = await create_workspace

    def mock_callback():
        pass

    mocker.patch.object(
        workspace.playground.ide_server_client,
        'agent_terminal_with_result',
        return_value='callback output'
    )

    result = await workspace.tools.run_cmd_with_soft_callback('echo test', 10, 60, mock_callback)
    assert result == 'callback output'

@pytest.mark.asyncio
async def test_workspace_run_cmd_output_truncation(mocker, create_workspace):
    """测试命令输出截断功能（211行）"""
    workspace = await create_workspace

    # 创建超过500行的输出
    long_output = '\n'.join([f'line {i}' for i in range(600)])

    mocker.patch.object(
        workspace.playground.ide_server_client,
        'agent_terminal_with_result',
        return_value=long_output
    )

    result = await workspace.tools.run_cmd('echo test')
    lines = result.split('\n')

    # 验证截断逻辑：前30行 + 截断提示 + 后470行
    assert len(lines) == 501  # 30 + 1(truncated message) + 470
    assert '... (100 lines truncated) ...' in result

@pytest.mark.asyncio
async def test_workspace_check_path_exists_edge_cases(mocker, create_workspace):
    """测试路径存在检查的边界情况（324-332行）"""
    workspace = await create_workspace

    # 模拟文件树数据
    mock_file_tree = [
        'file1.txt',
        'dir1/',
        'subdir/file2.py',
        'test_file.js'
    ]

    mocker.patch.object(workspace.tools, 'file_tree', return_value=mock_file_tree)

    # 测试通配符匹配
    assert await workspace.tools.check_path_exists('*.txt')
    assert await workspace.tools.check_path_exists('test_*')
    assert not await workspace.tools.check_path_exists('nonexistent*')

@pytest.mark.asyncio
async def test_workspace_read_file_edge_cases(mocker, create_workspace):
    """测试读取文件的边界情况（347-355行）"""
    workspace = await create_workspace

    # 测试非字符串响应
    mock_response = {'error': 'File not found'}
    mocker.patch.object(
        workspace.playground.ide_server_client,
        'agent_terminal_with_result',
        return_value=mock_response
    )

    result = await workspace.tools._run_cmd('cat nonexistent.txt', 10, 60, None)
    assert result == mock_response

@pytest.mark.asyncio
async def test_workspace_middlewares_not_empty_edge_case(mocker, create_workspace):
    """测试middlewares_not_empty的边界情况（424行）"""
    workspace = await create_workspace

    # 测试空middleware列表
    mocker.patch.object(workspace.tools, 'list_bound_middlewares_in_environment', return_value=[])
    result = await workspace.tools.middlewares_not_empty()
    assert result is False

    # 测试非空middleware列表
    mocker.patch.object(workspace.tools, 'list_bound_middlewares_in_environment', return_value=[{'name': 'test'}])
    result = await workspace.tools.middlewares_not_empty()
    assert result is True

@pytest.mark.asyncio
async def test_general_tool_callback(mocker, create_workspace):
    workspace = await create_workspace
    valid_tool_call = DotDict({
        "id": "123",
        "function": {
            "name": "test_function",
            "arguments": '{}'
        }
    })
    invalid_tool_call = DotDict({
        "id": "123",
    })

    mock_logger = mocker.patch.object(workspace.logger, 'error')

    await workspace.trigger_general_tool_callback(valid_tool_call, "end")
    mock_logger.assert_not_called()

    await workspace.trigger_general_tool_callback(invalid_tool_call, "end")
    mock_logger.assert_called_once()

@pytest.mark.asyncio
async def test_workspace_tools_lint_diagnostic(create_workspace, mocker):
    workspace = await create_workspace

    # Test successful lint diagnostic
    mock_diagnostics = {'data': [
        {'file': 'app.js', 'message': 'Missing semicolon', 'severity': 'warning'}
    ]}
    mock_lint = MagicMock()
    mock_lint.diagnostic = AsyncMock(return_value=mock_diagnostics)
    mocker.patch('heracles.agent_workspace.tools.get_env_var', return_value=True)
    mocker.patch.object(workspace, 'get_lint', AsyncMock(return_value=mock_lint))

    result = await workspace.tools.lint_diagnostic(['app.js'])
    assert result == mock_diagnostics
    mock_lint.diagnostic.assert_called_once_with(['app.js'])

    # Test when lint is not available
    mocker.patch('heracles.agent_workspace.tools.get_env_var', return_value=False)
    mocker.patch.object(workspace, 'get_lint', AsyncMock(return_value=None))
    result = await workspace.tools.lint_diagnostic(['app.js'])
    assert not result


@pytest.mark.asyncio
async def test_workspace_tools_lint_fix(create_workspace, mocker):
    workspace = await create_workspace

    # Test successful lint fix
    mock_fix_result = {'fixed_files': ['app.js'], 'errors': []}
    mock_lint = MagicMock()
    mock_lint.fix = AsyncMock(return_value=mock_fix_result)
    mocker.patch.object(workspace, 'get_lint', AsyncMock(return_value=mock_lint))

    result = await workspace.tools.lint_fix()
    assert result == mock_fix_result
    mock_lint.fix.assert_called_once()

    # Test when lint is not available
    mocker.patch.object(workspace, 'get_lint', AsyncMock(return_value=None))
    result = await workspace.tools.lint_fix()
    assert result is None


@pytest.mark.asyncio
async def test_workspace_tools_terminal_history(create_workspace, mocker):
    workspace = await create_workspace

    # Test with ANSI sequences and line endings cleanup
    mock_history = 'PaasNewLineSign\x1b[32mtest\x1b[0m\r\nline1\rline2\nline3'
    mocker.patch.object(workspace.tools.playground, 'func_call', return_value=mock_history)
    mocker.patch.object(workspace.playground.ide_server_client, 'agent_terminal_id', 'terminal-123')

    result = await workspace.tools.terminal_history(150)
    workspace.tools.playground.func_call.assert_called_with('agent_fetch_terminal_history', 'terminal-123', 150)
    assert 'test' in result
    assert 'PaasNewLineSign' not in result
    assert '\x1b[' not in result

    # Test default parameter
    await workspace.tools.terminal_history()
    workspace.tools.playground.func_call.assert_called_with('agent_fetch_terminal_history', 'terminal-123', 200)


@pytest.mark.asyncio
async def test_workspace_tools_browser_methods(create_workspace, mocker):
    workspace = await create_workspace

    # Test browser_goto
    mock_browser = MagicMock()
    mock_browser.goto = AsyncMock()
    mock_browser.screenshot = AsyncMock(return_value='screenshot_data')
    mock_browser.get_console_logs = AsyncMock(return_value=['log1', 'log2'])
    mocker.patch.object(workspace, 'get_browser', AsyncMock(return_value=mock_browser))
    workspace.browser = mock_browser

    await workspace.tools.browser_goto('https://example.com')
    mock_browser.goto.assert_called_once_with('https://example.com')

    # Test browser_screenshot
    result = await workspace.tools.browser_screenshot()
    assert result == 'screenshot_data'
    mock_browser.screenshot.assert_called_once()

    # Test browser_console_logs
    result = await workspace.tools.browser_console_logs()
    assert result == ['log1', 'log2']
    mock_browser.get_console_logs.assert_called_once()

    # Test when no browser available
    mocker.patch.object(workspace, 'get_browser', AsyncMock(return_value=None))

    result = await workspace.tools.browser_goto('https://example.com')
    assert result is False

    result = await workspace.tools.browser_screenshot()
    assert result is None

    result = await workspace.tools.browser_console_logs()
    assert result == []


@pytest.mark.asyncio
async def test_workspace_tools_list_changed_files(create_workspace, mocker):
    workspace = await create_workspace

    # Test with changed files
    mock_git_output = "src/app.js\ncomponents/header.js\nREADME.md"
    mocker.patch.object(
        workspace.playground.ide_server_client,
        'agent_terminal_with_result',
        return_value=mock_git_output
    )

    result = await workspace.tools.list_changed_files()
    workspace.playground.ide_server_client.agent_terminal_with_result.assert_called_once_with(
        "git status --porcelain | awk '{print $2}'"
    )
    assert result == ["src/app.js", "components/header.js", "README.md"]

    # Test with empty output
    mocker.patch.object(
        workspace.playground.ide_server_client,
        'agent_terminal_with_result',
        return_value=""
    )
    result = await workspace.tools.list_changed_files()
    assert result == []


@pytest.mark.asyncio
async def test_workspace_tools_web_operations_error_handling(create_workspace, mocker):
    """测试网页操作的错误处理"""
    workspace = await create_workspace

    # 测试read_webpage无API密钥
    mocker.patch('heracles.agent_workspace.tools.get_env_var', return_value=None)
    with pytest.raises(AgentRunException, match='JINA_API_KEY is not set'):
        await workspace.tools.read_webpage('https://example.com')

    # 测试search_internet无API密钥
    with pytest.raises(AgentRunException, match='JINA_API_KEY is not set'):
        await workspace.tools.search_internet('test query')


@pytest.mark.asyncio
async def test_workspace_tools_simple_error_cases(create_workspace):
    """测试工具方法的简单错误情况"""
    workspace = await create_workspace

    # 测试无效图片URL格式
    with pytest.raises(AgentRunException, match='Invalid image URL'):
        await workspace.tools.validate_image_url('ftp://example.com/image.png')

    with pytest.raises(AgentRunException, match='Invalid image URL'):
        await workspace.tools.validate_image_url('https://example.com/file.txt')


@pytest.mark.asyncio
async def test_workspace_tools_lint_error_handling(create_workspace, mocker):
    """测试lint错误获取的异常处理"""
    workspace = await create_workspace

    # 测试无诊断数据的情况
    mocker.patch.object(workspace.tools, 'list_changed_files', return_value=['test.py'])
    mocker.patch.object(workspace.tools, 'lint_diagnostic', return_value={})

    result = await workspace.tools.get_lint_errors()
    assert result == []


@pytest.mark.asyncio
async def test_workspace_get_browser_first_time(create_workspace, mocker):
    """Test get_browser method when browser is not initialized"""
    workspace = await create_workspace

    # Mock get_docker_url to return a valid URL
    mock_url = 'http://localhost:8080'
    mocker.patch.object(workspace.playground.ide_server_client, 'get_docker_url', return_value=mock_url)

    await workspace.get_browser()

@pytest.mark.asyncio
async def test_workspace_get_browser_no_docker_url(create_workspace, mocker):
    """Test get_browser method when docker URL is not available"""
    workspace = await create_workspace

    # Mock get_docker_url to return None
    mocker.patch.object(workspace.playground.ide_server_client, 'get_docker_url', return_value=None)
    result = await workspace.get_browser()
    # Should return None when docker URL is not available
    assert result is None

@pytest.mark.asyncio
async def test_workspace_get_lint_first_time(create_workspace, mocker):
    """Test get_lint method when lint is not initialized"""
    workspace = await create_workspace

    # Mock get_docker_url to return a valid URL
    mock_url = 'http://localhost:8080'
    mocker.patch.object(workspace.playground.ide_server_client, 'get_docker_url', return_value=mock_url)

    await workspace.get_lint()

@pytest.mark.asyncio
async def test_workspace_get_lint_no_docker_url(create_workspace, mocker):
    """Test get_lint method when docker URL is not available"""
    workspace = await create_workspace

    # Mock get_docker_url to return None
    mocker.patch.object(workspace.playground.ide_server_client, 'get_docker_url', return_value=None)

    result = await workspace.get_lint()
    # Should return None when docker URL is not available
    assert result is None
